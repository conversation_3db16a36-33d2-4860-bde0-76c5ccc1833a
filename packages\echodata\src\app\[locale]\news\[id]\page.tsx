import Image from 'next/image';

import productBgCenter from '@hi7/assets/background/news-bg.png';
import Link from '@hi7/components/Link';

function page() {
  return (
    <div className="flex items-center justify-center overflow-hidden bg-[#EDF7FF]">
      <div className="w-full">
        <div className="relative lg:min-h-[810px]">
          <div className="flex flex-col items-center justify-center py-11">
            <h1 className="mb-[55px] text-center text-[40px] leading-[48px] font-bold lg:text-[64px] lg:leading-[72px]">
              SCRM Champion系统
              <br />
              功能更新通知
            </h1>

            <div className="grid gap-[62px] px-5 lg:grid-cols-[420px_1fr] lg:px-[120px]">
              <Image
                width={420}
                height={420}
                src={productBgCenter}
                alt={''}
                className="object-contain"
              />
              <div>
                桌面端优化升级:
                <ol className="mb-5 list-decimal pl-5">
                  <li>
                    <b>【会话列表】升级:</b>
                    <br />
                    {'新增【刷新会话】按钮,优化交互流畅度并提升刷新响应效率'}
                  </li>
                  <li>
                    <b>【快捷回复】优化:</b>
                    <br />
                    {
                      '固定【直接发送/先翻译再发送】按钮,提升消息发送效率与便利性'
                    }
                  </li>
                  <li>
                    <b>【AI客服管理】升级:</b>
                    <br />
                    {'支持配置OpenAI平台,满足用户更多平台需求'}
                  </li>
                  <li>
                    <b>【在线聊天】升级:</b>
                    <br />
                    {'WABA聊天支持图片/视频接收和发送,提升沟通效率与营销灵活性'}
                  </li>
                  <li>
                    <b>【社群管理】升级:</b>
                    <br />
                    {
                      '️新增【一键进群】,支持WA/TG批量加群,助力营销引流、社群运营'
                    }
                  </li>
                  <li>
                    <b>【会话统计】升级:</b>
                    <br />
                    {
                      '新增切换查看维度按钮,支持按员工账号及绑定社交账号查看详情,更全面分析员工工作表现'
                    }
                  </li>
                </ol>
                <p className="mb-4">
                  如果你不会使用,我们技术工程师1V1教学【
                  <Link className="text-[#0506DC] underline" url="/contact-us">
                    联系我们
                  </Link>
                  】
                </p>
                <p className="mb-4">
                  🆕产品展示：分配专员一对一讲解我们的产品，从操作流程到效果展示，全方位展示我们的产品，让您快速上手，不用自己琢磨
                </p>
                <p className="mb-4">
                  🔈包教会：后续使用起来对于产品有不了解或者有疑问的，都可随时联系我们:{' '}
                  <a className="text-[#0506DC] underline">
                    https://007tg.com/ccs/champion
                  </a>
                </p>
                <p className="mb-4">
                  <a className="text-[#0506DC] underline">
                    SCRMChampion快速入门图文教程
                  </a>
                </p>
                <p>#SCRMChampion #AI客服 #社群运营 #在线聊天 #实时聊天记录</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default page;
