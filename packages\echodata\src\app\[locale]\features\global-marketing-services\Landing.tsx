import BannerLongRoundVector from '@hi7/assets/background/elongated-round-vector-lightblue.svg';
import BannerBgImg from '@hi7/assets/background/global-marketing-bg-img.png';
import BannerBgImgMobile from '@hi7/assets/background/global-marketing-bg-mobile-img.png';
import BannerRoundVector from '@hi7/assets/background/rounded-vector-lightblue.svg';
import AnimationFrame from '@hi7/components/AnimationFrame';
import type { DictionaryProps } from '@hi7/interface/i18n';
import { Arsenal } from 'next/font/google';
import Image from 'next/image';

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
  display: 'swap',
});

function Landing({ dictionary }: DictionaryProps) {

  return (
    <div className="relative flex items-center justify-center overflow-hidden">
      <div className="w-full">
        <div className="h-screen md:h-[45vh] md:mb-15 lg:h-[90vh] ">

          <div className="lg:flex w-full items-center justify-center">
            <Image
              src={BannerBgImg}
              alt={'background'}
              className="hidden md:block md:w-screen md:h-[50vh] lg:h-[80vh]"
            />
            <Image
              src={BannerBgImgMobile}
              alt={'background'}
              className="object-cover md:hidden w-full h-full"
            />
            <div className="w-[90%] absolute left-7 top-10 z-10 pr-7 text-[#04227D] text-start whitespace-break-spaces transition-opacity duration-700 md:right-7 md:top-10 md:text-end md:pr-0 xl:left-24 xl:top-25 ">
              <h1 className={`text-[40px] font-bold leading-[40px] mb-3 md:w-2/3 md:justify-self-end lg:text-[64px] lg:leading-[66px] xl:text-[77px] xl:leading-[68px] xl:mb-9 ${arsenal.className}`}>{dictionary.features.globalMarketingServices.banner.title}</h1>
              <div className="text-left items-center justify-center self-center md:text-right md:items-end md:justify-self-end md:w-2/3">
                <p className='font-[300] text-[16px] leading-[20px] lg:text-[24px] lg:leading-[33px] xl:text-[29px] xl:leading-[30px]'>{dictionary.features.globalMarketingServices.banner.desc}</p>
              </div>
              <hr className='w-[100%] my-4 md:mx-auto lg:w-2/3 lg:my-5 lg:mx-0 lg:justify-self-end xl:my-8' />
              <div className="mt-8 block lg:flex md:items-end md:justify-end md:justify-self-end">
                <a
                  href="#"
                  target="_blank"
                  className="mt-5 block w-auto max-w-[200px] text-center cursor-pointer rounded-[25px] bg-[#FF5542] py-1 px-[20px] whitespace-nowrap text-white hover:bg-[#C7E5FF] font-bold text-[18px] leading-[40px]"
                >
                  {dictionary.general.freeTrial.button1}
                </a>
                <a
                  href="https://scrmchampion.com/contact"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="mt-5 block w-auto max-w-[200px] text-center cursor-pointer rounded-[25px] bg-[#047AFF] py-1 px-[20px] whitespace-nowrap text-white hover:bg-[#C7E5FF] font-bold text-[18px] leading-[40px] lg:ml-5"
                >
                  {dictionary.general.freeTrial.button5}
                </a>
              </div>
            </div>


            <BannerLongRoundVector className="absolute scale-55 -right-43 bottom-30 md:hidden" />
            <BannerRoundVector className="absolute scale-55 right-16 bottom-50 md:hidden" />
          </div>
          <AnimationFrame
            variant="SlideUpAtEase"
            once={false}
            className="hidden lg:block"
          >
            <BannerLongRoundVector className="md:absolute lg:top-40 lg:left-90 lg:rotate-90 xl:top-40 xl:left-150 xl:scale-130" />
            <BannerLongRoundVector className="md:absolute lg:top-40 lg:left-15 lg:rotate-90 xl:top-40 xl:left-50 xl:scale-130" />
            <BannerRoundVector className="md:absolute left-0 scale-120 lg:top-40 xl:scale-160 xl:top-40 xl:left-10" />
          </AnimationFrame>
        </div>
      </div>
    </div >
  );
}

export default Landing;
