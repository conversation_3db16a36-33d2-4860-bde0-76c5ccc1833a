// /config/i18n.ts

export const i18n = {
  defaultLocale: 'en',
  defaultCountry: 'sg',
  locales: ['en', 'zh'],
} as const;

const dictionaries = {
  en: () =>
    import('@hi7/dictionaries/en.json').then((module) => module.default),
  zh: () =>
    import('@hi7/dictionaries/zh.json').then((module) => module.default),
};

export type Locale = (typeof i18n)['locales'][number];

// export const getDictionary = async (locale: Locale) =>
//   dictionaries[locale]?.() ?? dictionaries.en();
export const getDictionary = async (locale: string) => {
  if (!dictionaries[locale as keyof typeof dictionaries]) {
    return dictionaries['en'](); // fallback
  }
  return dictionaries[locale as keyof typeof dictionaries]();
};
