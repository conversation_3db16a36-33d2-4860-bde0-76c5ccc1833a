import Help1 from '@hi7/assets/icon/help-1.svg';
import Help2 from '@hi7/assets/icon/help-2.svg';
import Help3 from '@hi7/assets/icon/help-3.svg';
import type { DictionaryProps } from '@hi7/interface/i18n';
import TriggerAnimation from '../TriggerAnimation';

function Help({ dictionary }: DictionaryProps) {
  return (
    <TriggerAnimation>
      <div className="flex items-center justify-center overflow-hidden bg-[#C7E5FF] text-black">
        <div className="w-full">
          <div className="relative px-[55px] py-[80px] lg:min-h-[560px]">
            <div className="flex flex-col items-center justify-center pt-[70px] pb-[58px]">
              <h2 className="lg:animate-home-help-title text-center text-[32px] leading-[38px] font-bold lg:translate-y-[-100%] lg:text-[50px] lg:leading-[58px] lg:opacity-0">
                {dictionary.home.help.title}
              </h2>
            </div>
            <div className="grid items-center justify-center gap-y-[48px] lg:grid-cols-[280px_280px_280px] lg:gap-x-[120px]">
              <div className="lg:animate-home-help-0 flex flex-col items-center justify-center gap-2 lg:translate-x-[-100%] lg:opacity-0">
                <Help1 />
                <b className="text-lg">{dictionary.home.help.help1.title}</b>
                <p className="text-center">{dictionary.home.help.help1.desc}</p>
              </div>
              <div className="lg:animate-home-help-1 flex flex-col items-center justify-center gap-2 lg:translate-y-[100%] lg:opacity-0">
                <Help2 />
                <b className="text-lg">{dictionary.home.help.help2.title}</b>
                <p className="text-center">{dictionary.home.help.help2.desc}</p>
              </div>
              <div className="lg:animate-home-help-2 flex flex-col items-center justify-center gap-2 lg:translate-x-[100%] lg:opacity-0">
                <Help3 />
                <b className="text-lg">{dictionary.home.help.help3.title}</b>
                <p className="text-center">{dictionary.home.help.help3.desc}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </TriggerAnimation>
  );
}

export default Help;
