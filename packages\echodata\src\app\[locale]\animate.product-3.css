:root {
  --product-animation-duration: 1.5s;
  --product-animation-half-duration: 0.75s;
}

@theme {
  --animate-product-3-bg-left-1: product3BgLeft1
    var(--product-animation-duration) ease-in-out forwards;
  @keyframes product3BgLeft1 {
    0% {
      transform: translateX(0);
    }

    100% {
      transform: translateX(100%);
    }
  }

  --animate-product-3-bg-left-2: product3BgLeft2
    var(--product-animation-duration) var(--product-animation-half-duration)
    ease-in-out forwards;
  @keyframes product3BgLeft2 {
    0% {
      opacity: 0;
      transform: translateX(0);
    }

    100% {
      opacity: 1;
      transform: translateX(100%);
    }
  }

  --animate-product-3-picture: product3Picture var(--product-animation-duration)
    ease-in-out forwards;
  @keyframes product3Picture {
    0% {
      transform: translateX(100%);
    }

    100% {
      transform: translateX(0);
    }
  }

  --animate-product-3-title: product3Title var(--product-animation-duration)
    var(--product-animation-half-duration) ease-in-out forwards;
  @keyframes product3Title {
    0% {
      opacity: 0;
      transform: translateX(-100%);
    }

    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }

  --animate-product-3-bg-right: product3BgRight
    var(--product-animation-duration) var(--product-animation-half-duration)
    ease-in-out forwards;
  @keyframes product3BgRight {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }

  --animate-product-3-bottom-ring-left: product3BottomRingLeft
    var(--product-animation-duration) ease-in-out forwards;
  @keyframes product3BottomRingLeft {
    0% {
      transform: translateX(0%);
    }

    100% {
      transform: translateX(50%);
    }
  }

  --animate-product-3-bottom-ring-right: product3BottomRingRight
    var(--product-animation-duration) ease-in-out forwards;
  @keyframes product3BottomRingRight {
    0% {
      transform: translateX(100%);
    }

    100% {
      transform: translateX(-105%);
    }
  }

  --animate-product-3-bottom-title: product3BottomTitle
    var(--product-animation-duration) ease-in-out forwards;
  @keyframes product3BottomTitle {
    0% {
      opacity: 0;
      transform: translateY(-100%) scale(0.8);
    }

    100% {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  --animate-product-3-bottom-content: product3BottomContent
    var(--product-animation-duration) ease-in-out forwards;
  @keyframes product3BottomContent {
    0%,
    50% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }
}
