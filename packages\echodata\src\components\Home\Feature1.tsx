import FeatureBgRingLeft from '@hi7/assets/background/feature-ring-left.svg';
import FeaturegRingRight from '@hi7/assets/background/feature-ring-right.svg';
import Feature1 from '@hi7/assets/icon/feature-1.svg';
import Feature2 from '@hi7/assets/icon/feature-2.svg';
import Feature3 from '@hi7/assets/icon/feature-3.svg';
import Feature4 from '@hi7/assets/icon/feature-4.svg';
import type { DictionaryProps } from '@hi7/interface/i18n';
import TriggerAnimation from '../TriggerAnimation';

function Feature({ dictionary }: DictionaryProps) {
  return (
    <TriggerAnimation>
      <div className="flex items-center justify-center overflow-hidden bg-linear-[180deg,#0A0FDD_0%,#818BEC_100%]">
        <div className="w-full">
          <div className="relative lg:min-h-[920px]">
            <div className="lg:animate-home-feature-1-ring-left absolute top-[-20%] bottom-[-15%] w-[45%] opacity-0 lg:top-[40px] lg:left-[0px] lg:opacity-0">
              <FeatureBgRingLeft width="100%" />
            </div>
            <div className="lg:animate-home-feature-1-ring-right absolute bottom-[-15%] w-[45%] lg:top-[40px] lg:right-[0px] lg:opacity-0">
              <FeaturegRingRight width="100%" />
            </div>
            <div className="flex flex-col items-center justify-center pt-[78px] text-white lg:pt-44">
              <h2 className="lg:animate-home-feature-1-title mb-16 text-[32px] leading-[38.4px] font-bold lg:translate-y-[-100%] lg:text-[50px] lg:leading-[58px] lg:opacity-0">
                {dictionary.home.feature1.title}
              </h2>

              <div className="relative grid gap-y-[42px] pb-[42px] lg:grid-cols-2 lg:gap-x-[210px] lg:gap-y-[150px] lg:pb-0">
                <div className="lg:animate-home-feature-1-points flex flex-col-reverse items-start justify-center gap-4 lg:flex-row lg:opacity-0">
                  <div className="max-w-[370px]">
                    <h4 className="mb-2.5 text-[22px] font-bold">
                      {dictionary.home.feature1.chatTranslation.title}
                    </h4>
                    <ol className="space-y-1 text-[14px]">
                      <li>
                        1. {dictionary.home.feature1.chatTranslation.desc1}
                      </li>
                      <li>
                        2. {dictionary.home.feature1.chatTranslation.desc2}
                      </li>
                      <li>
                        3. {dictionary.home.feature1.chatTranslation.desc3}
                      </li>
                    </ol>
                  </div>
                  <div className="flex h-[100px] w-[100px] items-center justify-center rounded-full bg-white">
                    <Feature1 />
                  </div>
                </div>
                <div className="lg:animate-home-feature-1-points flex flex-col items-start justify-center gap-4 lg:flex-row lg:opacity-0">
                  <div className="flex h-[100px] w-[100px] items-center justify-center rounded-full bg-white">
                    <Feature2 />
                  </div>
                  <div className="max-w-[370px]">
                    <h4 className="mb-2.5 text-[22px] font-bold">
                      {dictionary.home.feature1.trafficDistribution.title}
                    </h4>
                    <ol className="space-y-1 text-[14px]">
                      <li>
                        1. {dictionary.home.feature1.trafficDistribution.desc1}
                      </li>
                      <li>
                        2. {dictionary.home.feature1.trafficDistribution.desc2}
                      </li>
                      <li>
                        3. {dictionary.home.feature1.trafficDistribution.desc3}
                      </li>
                    </ol>
                  </div>
                </div>

                <div className="lg:animate-home-feature-1-points flex flex-col-reverse items-start justify-center gap-4 lg:flex-row lg:opacity-0">
                  <div className="max-w-[370px]">
                    <h4 className="mb-2.5 text-[22px] font-bold">
                      {dictionary.home.feature1.marketingAssistant.title}
                    </h4>
                    <ol className="space-y-1 text-[14px]">
                      <li>
                        1. {dictionary.home.feature1.marketingAssistant.desc1}
                      </li>
                      <li>
                        2. {dictionary.home.feature1.marketingAssistant.desc2}
                      </li>
                      <li>
                        3. {dictionary.home.feature1.marketingAssistant.desc3}
                      </li>
                    </ol>
                  </div>
                  <div className="flex h-[100px] w-[100px] items-center justify-center rounded-full bg-white">
                    <Feature3 />
                  </div>
                </div>
                <div className="lg:animate-home-feature-1-points flex flex-col items-start justify-center gap-4 lg:flex-row lg:opacity-0">
                  <div className="flex h-[100px] w-[100px] items-center justify-center rounded-full bg-white">
                    <Feature4 />
                  </div>
                  <div className="max-w-[370px]">
                    <h4 className="mb-2.5 text-[22px] font-bold">
                      {dictionary.home.feature1.teamManagement.title}
                    </h4>
                    <ol className="space-y-1 text-[14px]">
                      <li>{dictionary.home.feature1.teamManagement.desc1}</li>
                      <li>{dictionary.home.feature1.teamManagement.desc2}</li>
                      <li>{dictionary.home.feature1.teamManagement.desc3}</li>
                    </ol>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </TriggerAnimation>
  );
}

export default Feature;
