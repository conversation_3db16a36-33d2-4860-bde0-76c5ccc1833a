import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);

type FormatDateParams = {
  date: string | Date | Dayjs;
  format?: string;
  isUtc?: boolean;
};

export const formatDate = ({
  date,
  format = 'YYYY-MM-DD HH:mm',
  isUtc = false,
}: FormatDateParams) => {
  const currentDate = isUtc ? dayjs.utc(date) : dayjs(date);
  if (!currentDate.isValid()) {
    return '';
  }

  return currentDate.format(format);
};
