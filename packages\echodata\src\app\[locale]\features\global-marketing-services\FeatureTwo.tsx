import ImageBgMobile from '@hi7/assets/background/global-marketing-bg-mobile.png';
import ImageBg from '@hi7/assets/background/global-marketing-bg.png';
import EmailMarketing from '@hi7/assets/icon/email-marketing.svg';
import SMSMarketing from '@hi7/assets/icon/sms-marketing.svg';
import VoiceMarketing from '@hi7/assets/icon/voice-marketing.svg';
import type { DictionaryProps } from '@hi7/interface/i18n';
import { Arsenal } from 'next/font/google';
import Image from 'next/image';


const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
  display: 'swap',
});

function FeatureTwo({ dictionary }: DictionaryProps) {
  const cardGroups = [
    {
      icon: VoiceMarketing,
      items: [
        {
          title: "Global Voice Broadcasting",
          desc: "Reach users in major countries with stable, uninterrupted voice connections and real-time call status tracking."
        },
        {
          title: "Text-to-Speech (TTS) Support",
          desc: "Solve SMS delivery challenges effectively by scheduling voice calls tailored to customer time zones."
        },
      ],
    },
    {
      icon: SMSMarketing,
      items: [
        {
          title: "Global Bulk SMS Messaging",
          desc: "Utilise high-quality international SMS marketing channels with direct connections to local operators. Also supports OTP bulk messaging."
        },
        {
          title: "Selected Country Marketing Channels",
          desc: "Utilise high-quality international SMS marketing channels with direct connections to local operators. Also supports OTP bulk messaging."
        },
      ],
    },
    {
      icon: EmailMarketing,
      items: [
        {
          title: "Global Bulk Email Messaging",
          desc: "Deliver professional, user-friendly email marketing campaigns worldwide with ease."
        },
        {
          title: "Simple Interface and Comprehensive Reports",
          desc: "Enjoy a user-friendly interface with detailed performance reports."
        },
        {
          title: "High Inbox Rate",
          desc: "Achieve up to a 98% inbox delivery rate for bulk email campaigns."
        },
        {
          title: "Support for Various Industries",
          desc: "Tailored content and strategies for diverse marketing needs, regardless of industry."
        },
      ],
    }
  ];

  return (
    <div className="min-h-screen flex items-center justify-center md:mt-10">
      <div className="w-full">
        <div className="h-full">
          <div className="relative min-h-screen">
            <div className="relative h-full w-full">
              <Image
                src={ImageBgMobile}
                alt={"background"}
                className="object-cover w-[100vw] lg:hidden "
                priority
              />

              <Image
                src={ImageBg}
                alt={'background'}
                className="object-cover hidden w-[100vw] lg:block lg:scale-y-[1.05] lg:mb-[50px] lg:mt-30 lg:scale-y-[1] xl:mt-15"
              />
            </div>

            {/******* Content Overlay *******/}
            <div className={`w-full text-start text-white transition-opacity duration-700 md:static md:top-3/5 lg:absolute lg:top-30 lg:h-[100%] xl:top-50`}>
              <h2 className={`w-[70%] absolute right-5 top-50 text-[40px] font-bold text-right leading-[42px] px-4 ${arsenal.className} md:top-100 md:text-[56px] md:leading-[60px] md:w-[80%] lg:static lg:w-full lg:-top-[30px] lg:px-15 xl:text-[70px] xl:px-20`}>
                We Help You in These Areas
              </h2>
              <div className="relative min-h-screen bg-[#047AFF] lg:bg-transparent px-8 -mb-3 pt-10 -mt-20 md:-mt-40 md:pb-20 lg:mt-0 lg:px-15 z-[2] xl:pt-15 xl:px-20">
                <div className="block gap-6 items-stretch justify-center lg:flex lg:flex-row lg:flex-row-reverse lg:gap-10">
                  {cardGroups.map((group, index) => (
                    <div
                      className={`w-full p-6 bg-white text-[#FF5C00] rounded-[20px] h-full flex flex-col xl:px-10
                      ${index === cardGroups.length - 1 ? 'lg:grid lg:w-[108%] self-end' : 'mb-8 lg:w-[60%] lg:mb-0  lg:h-[75vh] xl:h-[50vh]'} `}
                    >
                      <group.icon className="mb-3" />
                      <div
                        key={index}
                        className={`flex flex-col justify-between flex-1
                        ${index === cardGroups.length - 1 ? 'lg:grid lg:grid-cols-2 gap-5 w-full xl:gap-7' : 'lg:w-full h-full'}`}
                      >
                        {group.items.map((item, idx) => (
                          <div key={idx} className="flex flex-col h-full">
                            <h3 className="text-[23px] font-medium text-[#FF5C00] leading-[26px] lg:text-[20px] lg:leading-[24px]">
                              {item.title}
                            </h3>
                            <hr className="my-3" />
                            <p className="text-[18px] font-light text-[#111111] leading-[22px] mb-10 lg:text-[15px] lg:leading-[20px] lg:mb-4">
                              {item.desc}
                            </p>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div >
  );
}

export default FeatureTwo;
