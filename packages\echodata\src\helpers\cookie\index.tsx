import COOKIES, { COOKIE_OPTIONS } from '@hi7/configs/cookies';
import type {
  RequestCookies,
  ResponseCookie,
  ResponseCookies,
} from 'next/dist/compiled/@edge-runtime/cookies';
import { cookies } from 'next/headers';
import type { NextRequest, NextResponse } from 'next/server';

/** For Server Actions */
export const setCookies = async (
  name: string,
  value: string,
  options?: Partial<ResponseCookie>,
) => {
  cookies().set(name, value, { ...COOKIE_OPTIONS, ...options });
};

export const getCookies = async (
  name: string,
  prefix: string | boolean = COOKIES.common,
) => {
  return cookies().get(prefix ? `${prefix}-${name}` : name);
};

/** For Middleware */
export class CookieHandler {
  reqCookies: RequestCookies;
  resCookies: ResponseCookies;
  constructor(request: NextRequest, response: NextResponse) {
    this.reqCookies = request.cookies;
    this.resCookies = response.cookies;
  }

  private getName(name: string, prefix: string | boolean = COOKIES.common) {
    return prefix
      ? `${prefix !== COOKIES.common ? prefix : COOKIES.common}-${name}`
      : name;
  }

  /**
   * @param prefix -
   *      - default=`hi7`
   *      - if pass as boolean e.g. `false` then will exclude prefix
   */
  getCookie(name: string, prefix: string | boolean = COOKIES.common) {
    const cookieName = this.getName(name, prefix);

    return this.reqCookies.get(cookieName);
  }

  getAllCookies(prefix: string = COOKIES.common) {
    const fullPrefix =
      prefix !== COOKIES.common ? `${prefix}-${COOKIES.common}` : prefix;

    return this.reqCookies
      .getAll()
      .filter((item) => item.name.startsWith(fullPrefix))
      .map((item) => ({
        ...item,
        name: item.name.replace(fullPrefix, '').replace('-', ''),
      }));
  }

  /**
   * @param prefix -
   *      - default=`hi7`
   *      - if pass as boolean e.g. `false` then will exclude prefix
   */
  setCookie(cookie: ResponseCookie, prefix: string | boolean = COOKIES.common) {
    const name = this.getName(cookie.name, prefix);

    return this.resCookies.set({
      ...COOKIE_OPTIONS,
      ...cookie,
      name,
    });
  }

  deleteCookie(name: string) {
    return this.resCookies.delete({ name, domain: COOKIE_OPTIONS.domain });
  }
}
