import type { Dictionary } from '@hi7/interface/dictionary';
import type { LocaleProps } from '@hi7/interface/i18n';
import { getDictionary } from '@hi7/lib/i18n';
import IndustryInsights from './insights';

async function page({ params }: LocaleProps) {
  const { locale } = params;
  const dictionary: Dictionary = await getDictionary(locale);
  let categories = [
    dictionary.industryInsights.categories.allArticles,
    dictionary.industryInsights.categories.customerAcquisition,
    dictionary.industryInsights.categories.accountDataFiltering,
    dictionary.industryInsights.categories.massMarketing,
    dictionary.industryInsights.categories.socialMediaMarketing,
  ];

  if (locale === 'zh') {
    categories = [
      ...categories,
      dictionary.industryInsights.categories.marketingResearch,
      dictionary.industryInsights.categories.selectedTopics,
      dictionary.industryInsights.categories.serviceSupport,
    ];
  }

  return <IndustryInsights dictionary={dictionary} locale={locale} categories={categories} />;
}

export default page;
