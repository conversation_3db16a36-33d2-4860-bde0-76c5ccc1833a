type HTMLTermsClassType = Partial<Record<keyof HTMLElementTagNameMap, string>>;

export const HTML_CLASS_TERMS_PRIVACY: HTMLTermsClassType = {
  h3: 'py-1 pb-7 text-[32px] lg:text-[42px] font-bold',
  h5: 'py-4 text-xl font-bold',
  h6: 'py-1 pb-7 text-base text-[#8C8C8C]',
  p: 'py-2 text-lg leading-6',
  ol: 'ml-7 py-4 text-xl',
  li: 'list-decimal',
  a: 'text-hi7-primary',
  b: 'font-semibold',
  strong: 'font-semibold',
};

export const HTML_CLASS_POST_DETAILS: HTMLTermsClassType = {
  a: 'text-hi7-primary',
  h2: 'text-[22px] lg:text-[32px] font-bold leading-[40px]',
  p: 'lg:text-[18px]',
  ol: 'list-decimal',
  ul: 'list-disc',
  li: 'ms-5',
};
