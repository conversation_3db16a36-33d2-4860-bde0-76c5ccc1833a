:root {
  --product-animation-duration: 1.5s;
  --product-animation-half-duration: 0.75s;
}

@theme {
  --animate-product-1-white-bg: product1WhiteBg
    var(--product-animation-duration) ease-in-out forwards;
  @keyframes product1WhiteBg {
    0% {
      opacity: 0;
      transform: translateY(-100%);
    }

    50% {
      opacity: 1;
      transform: translateY(0);
      width: 100dvw;
      margin-left: -12px;
      margin-right: -12px;
    }

    100% {
      opacity: 1;
      width: calc(100dvw - 420px);
      margin-left: 0px;
      margin-right: 0px;
    }
  }

  --animate-product-1-white-content: product1WhiteContent
    var(--product-animation-duration) ease-in-out forwards;
  @keyframes product1WhiteContent {
    50% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }

  --animate-product-1-white-picture: product1WhitePicture
    var(--product-animation-duration) ease-in-out forwards;
  @keyframes product1WhitePicture {
    50% {
      opacity: 0;
      transform: translateX(100%);
    }

    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }

  --animate-product-1-top-ring-left: product1TopRingLeft
    var(--product-animation-half-duration) ease-in-out forwards;
  @keyframes product1TopRingLeft {
    0% {
      transform: rotate(45deg) translateX(-100%);
    }

    100% {
      transform: rotate(0deg) translateX(0);
    }
  }

  --animate-product-1-top-ring-right: product1TopRingRight
    var(--product-animation-half-duration) ease-in-out forwards;
  @keyframes product1TopRingRight {
    0% {
      transform: rotate(45deg) translateX(150%);
    }

    100% {
      transform: rotate(0deg) translateX(0);
    }
  }

  --animate-product-1-bottom-ring-left: product1BottomRingLeft
    var(--product-animation-duration) ease-in-out forwards;
  @keyframes product1BottomRingLeft {
    0% {
      opacity: 0;
      transform: rotate(45deg) translateX(-200%);
    }

    100% {
      opacity: 1;
      transform: rotate(0deg) translateX(0);
    }
  }

  --animate-product-1-bottom-ring-right: product1BottomRingRight
    var(--product-animation-duration) ease-in-out forwards;
  @keyframes product1BottomRingRight {
    0% {
      opacity: 0;
      transform: rotate(45deg) translateX(150%);
    }

    100% {
      opacity: 1;
      transform: rotate(0deg) translateX(0);
    }
  }

  --animate-product-1-bottom-title: product1BottomTitle
    var(--product-animation-duration) ease-in-out forwards;
  @keyframes product1BottomTitle {
    0% {
      opacity: 0;
      transform: translateY(-100%) scale(0.8);
    }

    100% {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  --animate-product-1-bottom-content: product1BottomContent
    var(--product-animation-duration) ease-in-out forwards;
  @keyframes product1BottomContent {
    0%,
    50% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }
}
