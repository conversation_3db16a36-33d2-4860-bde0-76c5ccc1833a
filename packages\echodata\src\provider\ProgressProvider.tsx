'use client';

import { isCSR } from '@hi7/helpers/ssr';
import { AppProgressBar as ProgressBar } from 'next-nprogress-bar';

type ProgressProviderProps = { children?: React.ReactNode };

const ProgressProvider = ({ children }: ProgressProviderProps) => {
  const isCsr = isCSR();

  return (
    <>
      {children}

      {isCsr && (
        <ProgressBar
          height="4px"
          color="#FFD900"
          options={{ showSpinner: false }}
          shallowRouting
        />
      )}
    </>
  );
};

export default ProgressProvider;
