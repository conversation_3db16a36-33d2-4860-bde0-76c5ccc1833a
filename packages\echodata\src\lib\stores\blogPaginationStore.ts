import { create } from 'zustand';

interface PaginationStore {
  currentPage: number;
  itemsPerPageOptions: number[];
  selectedItemsPerPage: number;
  setCurrentPage: (page: number) => void;
  setSelectedItemsPerPage: (items: number) => void;
  resetPagination: () => void;
}

const DEFAULT_VALUES = {
  currentPage: 1,
  itemsPerPageOptions: [5, 10, 20, 50], // example options for items per page
  selectedItemsPerPage: 10, // default items per page
};

const usePaginationStore = create<PaginationStore>((set) => ({
  ...DEFAULT_VALUES,
  setCurrentPage: (page) => set({ currentPage: page }),
  setSelectedItemsPerPage: (items) =>
    set({ selectedItemsPerPage: items, currentPage: 1 }), // reset to page 1
  resetPagination: () => set({ ...DEFAULT_VALUES }),
}));

export default usePaginationStore;
