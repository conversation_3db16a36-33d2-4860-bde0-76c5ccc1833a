import type { Dictionary } from '@hi7/interface/dictionary';
import { LocaleProps } from '@hi7/interface/i18n';
import { getDictionary } from '@hi7/lib/i18n';
import HelpList from './helpList';

async function page({ params }: LocaleProps) {
  const { locale } = params;
  const dictionary: Dictionary = await getDictionary(locale);

  return <HelpList dictionary={dictionary} locale={locale} />;
}

export default page;
