import type { DictionaryProps } from '@hi7/interface/i18n';
import { Arsenal } from 'next/font/google';

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
});

function GetStarted({ dictionary }: DictionaryProps) {
  return (
    <div className="flex items-center justify-center overflow-hidden bg-linear-[180deg,#047AFF_0%,#047AFF_20%,#04227D_50%,#04227D_100%] text-white h-screen md:bg-linear-[180deg,#04227D_100%] md:h-[350px] md:relative lg:bg-linear-[180deg,#047AFF_0%,#047AFF_15%,#04227D_50%,#04227D_100%] lg:h-[90vh] lg:-mt-[60px] xl:h-[70vh] xl:bg-linear-[180deg,#047AFF_0%,#047AFF_10%,#04227D_50%,#04227D_100%]">
      <div className="w-full">
        <div className="relative min-h-[500px]">
          <div className="flex flex-col items-center justify-center pt-[84px] text-center px-10 md:pt-[100px] lg:px-5 lg:pt-[165px] xl:pt-[200px]">
            <h2 className={`${arsenal.className} mb-2.5 text-[40px] leading-[42px] font-bold lg:text-[64px] lg:leading-[60px]`}>
              {dictionary.features.globalMarketingServices.getStarted.title}
            </h2>
          </div>
          <div className="mt-5 flex flex-col items-center justify-center md:flex-row md:gap-5">
            <a
              href="https://scrmchampion.com/contact"
              target="_blank"
              rel="noopener noreferrer"
              className="block w-auto text-center cursor-pointer rounded-[25px] bg-[#FF5542] py-0 px-[30px] whitespace-nowrap text-white hover:bg-[#C7E5FF] font-bold text-[18px] leading-[45px]"
            >
              {dictionary.general.freeTrial.button1}
            </a>
            <a
              href="https://scrmchampion.com/contact"
              target="_blank"
              rel="noopener noreferrer"
              className="mt-5 block w-auto text-center cursor-pointer rounded-[25px] bg-[#047AFF] py-0 px-[30px] whitespace-nowrap text-white hover:bg-[#C7E5FF] font-bold text-[18px] leading-[45px] md:mt-0 lg:mt-0"
            >
              {dictionary.general.freeTrial.button5}
            </a>

          </div>
        </div>
      </div>
    </div >
  );
}

export default GetStarted;
