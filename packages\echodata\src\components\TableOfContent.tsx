'use client';

import { generateIdByText, processHtmlContent } from '@hi7/helpers/html';
import useScreenSize from '@hi7/helpers/useScreenSize';
import clsx from 'clsx';
import type React from 'react';
import type { ReactElement } from 'react';
import { useLayoutEffect, useRef, useState } from 'react';

interface TOCItem {
  id: string;
  text: ReactElement;
  level: number;
}

interface TableOfContentsProps {
  content: string;
  offset?: number;
}

const navItemPrefix = 'nav-';

const parseH2FromContent = (content: string): TOCItem[] => {
  // Match h2 tags and their content
  const h2Regex = /<h2.*?>(.*?)<\/h2>/g;
  const headings: TOCItem[] = [];
  let match;

  while ((match = h2Regex.exec(content)) !== null) {
    // Remove any HTML tags inside the heading text
    const text = processHtmlContent(match[1].replace(/<\/?[^>]+(>|$)/g, ''));

    // TODO: update processedText type
    const id = generateIdByText(text as unknown as string);

    headings.push({
      id,
      text,
      level: 2,
    });
  }

  return headings;
};

const TableOfContents: React.FC<TableOfContentsProps> = ({ content }) => {
  const headings = parseH2FromContent(content);
  const menuRef = useRef<HTMLUListElement>(null);
  const [activeId, setActiveId] = useState<string>(headings.at(0)?.id || '');

  const { isMobile } = useScreenSize();
  const offset = 120;

  // Function to smoothly scroll to a heading with an offset
  const scrollToHeading = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      const top =
        element.getBoundingClientRect().top + window.pageYOffset - offset;
      window.scrollTo({ top, behavior: 'smooth' });
    }
  };

  // UseLayoutEffect to set up intersection observer and manage active section based on scroll
  useLayoutEffect(() => {
    const headerElements = parseH2FromContent(content);

    if (window.innerHeight - offset < 0) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setActiveId(entry.target.id);
          }
        });
      },
      {
        rootMargin: `-${offset}px 0px -${window.innerHeight - offset}px 0px`,
      },
    );

    // Observe each heading element
    headerElements.forEach((item) => {
      const element = document.getElementById(item.id);
      if (element) {
        observer.observe(element);
      }
    });

    return () => {
      observer.disconnect();
    };
  }, [content, offset, isMobile]);

  // Render nothing if there are no headings
  if (headings.length === 0) return null;

  return (
    <nav className="bg-hi7-light sticky left-0 top-10 z-10 lg:top-24">
      <ul
        className="scrollbar-hide flex gap-4 space-x-4 overflow-x-auto lg:flex-col lg:space-x-0 lg:space-y-2 lg:px-6"
        ref={menuRef}
      >
        {headings.map((heading) => (
          <li
            id={`${navItemPrefix}${heading.id}`}
            key={heading.id}
            className={clsx('text-nowrap lg:text-wrap lg:px-0', {
              'text-hi7-primary ms-6 list-disc lg:ms-0':
                activeId === heading.id,
            })}
          >
            <a
              href={`#${heading.id}`}
              onClick={(e) => {
                e.preventDefault();
                scrollToHeading(heading.id);
              }}
              className={clsx(
                'block',
                'text-gray-600',
                'lg:hover:text-hi7-golden',
                'transition-colors lg:text-[18px]',
                { 'text-hi7-primary': activeId === heading.id },
              )}
            >
              {heading.text}
            </a>
          </li>
        ))}
      </ul>
    </nav>
  );
};

export default TableOfContents;
