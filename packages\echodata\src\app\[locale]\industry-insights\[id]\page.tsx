'use client';

import type { Dictionary } from '@hi7/interface/dictionary';
import type { LocaleProps } from '@hi7/interface/i18n';
import { getDictionary } from '@hi7/lib/i18n';
import ClientArticle from './article';
import GetStarted from './GetStarted';

async function page({ params }: LocaleProps) {
  const { locale } = params;
  const dictionary: Dictionary = await getDictionary(locale);

  return (
    <div className="relative bg-[#e9f3ff]">
      <ClientArticle dictionary={dictionary} locale={locale} />
      <GetStarted dictionary={dictionary} />
    </div>
  );
}

export default page;
