import {
  DEFAULT_INPUT_TYPE,
  LABEL_FLOATING_FONT_SIZE,
  LABEL_FLOATING_TOP,
  LABEL_FONT_SIZE,
} from '@hi7/configs/input';
import type { InputType } from '@hi7/interface/input';
import type React from 'react';
import { type ReactNode } from 'react';

type LabelProps = {
  children: string | ReactNode;
  name: string;
  required?: boolean;
  isFocused?: boolean;
  variant: InputType;
  value: unknown;
  classes?: string;
};

const INPUT_STYLE: Record<InputType, string> = {
  Outlined: '',
  Standard: 'absolute transition-all duration-200',
};

const Label: React.FC<LabelProps> = ({
  children,
  name,
  required,
  isFocused,
  variant = DEFAULT_INPUT_TYPE,
  value,
  classes = '',
}) => {
  const isFloat = isFocused || value;
  return (
    <label
      htmlFor={name}
      className={`text-hi7-label block leading-normal ${INPUT_STYLE[variant]} ${
        variant === 'Standard'
          ? `label-standard ${
              isFloat
                ? `text-[${LABEL_FLOATING_FONT_SIZE}] text-hi7-label-floating`
                : `text-[${LABEL_FONT_SIZE}] text-hi7-label-standard`
            } `
          : `text-[1rem]`
      } ${classes} `}
      style={{ top: isFloat ? LABEL_FLOATING_TOP : '0.688rem' }}
    >
      {children}

      {required && <span className={`text-hi7-error`}>*</span>}
    </label>
  );
};

export default Label;
