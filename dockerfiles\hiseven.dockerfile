FROM public.ecr.aws/h8h1e3y7/node-builder:20-20240724 as builder

WORKDIR /app

ARG VERSION=unknown \
    ENV=stg \
    NPM_TOKEN \
    AWS_ACCESS_KEY_ID \
    AWS_SECRET_ACCESS_KEY \
    AWS_DEFAULT_REGION

ENV VERSION=$VERSION \
    ENV=$ENV \
    NPM_TOKEN=$NPM_TOKEN \
    AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID \
    AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY \
    AWS_DEFAULT_REGION=$AWS_DEFAULT_REGION \
    ASSET_PREFIX=https://assets.hiseven.com/hiseven.com

COPY . .

RUN pnpm install
RUN pnpm hiseven:build:$ENV

RUN aws s3 cp packages/hiseven/.next/static s3://assets-steady-salmon/hiseven.com/_next/static --recursive --exclude "*.woff2"
RUN aws s3 cp packages/hiseven/.next/static s3://assets-steady-salmon/hiseven.com/_next/static --recursive --exclude "*" --include "*.woff2" --content-type "font/woff2"

FROM node:20-alpine as runtime

WORKDIR /app

COPY --from=builder /app/packages/hiseven/.next/standalone/packages/hiseven ./
COPY --from=builder /app/packages/hiseven/.next/standalone ./

RUN rm -rf /app/packages

EXPOSE 3000
CMD ["sh", "-c", "node server.js"]
