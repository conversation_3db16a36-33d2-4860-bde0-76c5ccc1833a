/** @type {import('next').NextConfig} */
const nextConfig = {
  env: {
    NEXT_PUBLIC_VERSION: process.env.VERSION,
    NEXT_PUBLIC_HI7_GA_TAG: process.env.HI7_GA_TAG,
  },
  output: 'standalone',
  assetPrefix: process.env.ASSET_PREFIX,
  reactStrictMode: true,
  webpack(config) {
    // Reference: https://react-svgr.com/docs/next/
    const fileLoaderRule = config.module.rules.find((rule) =>
      rule.test?.test?.('.svg'),
    );

    const svgRules = [
      {
        ...fileLoaderRule,
        test: /\.svg$/i,
        resourceQuery: /url/,
      },
      {
        test: /\.svg$/i,
        issuer: fileLoaderRule.issuer,
        resourceQuery: { not: [...fileLoaderRule.resourceQuery.not, /url/] },
        use: ['@svgr/webpack'],
      },
    ];
    config.module.rules.push(...svgRules);

    fileLoaderRule.exclude = /\.svg$/i;
    return config;
  },
  async redirects() {
    return [
      {
        source: '/',
        destination: '/en',
        permanent: false,
      },
    ];
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
};

export default nextConfig;
