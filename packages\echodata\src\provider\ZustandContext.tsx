'use client';

import type { FeatureFlag } from '@hi7/interface/feature-flag';
import useFeatureFlagStore from '@hi7/lib/stores/featureFlagStore';
import { useEffect } from 'react';
import { create } from 'zustand';

type GlobalState = {
  baseurl: string;
};

type SetGlobalState = {
  init: (state: GlobalState) => void;

  setBaseurl: (baseurl: string) => void;
};

type GlobalStore = GlobalState & SetGlobalState;

export const useGlobalStore = create<GlobalStore>((set) => ({
  init: (state: GlobalState) => set(state),

  baseurl: '',
  setBaseurl: (baseurl) => set({ baseurl }),
}));

function ZustandContext({
  children,
  config,
  featureFlag,
}: {
  children: React.ReactNode;
  config: GlobalState;
  featureFlag: FeatureFlag;
}) {
  const { init: initGlobal } = useGlobalStore();
  const { init: initFeatureFlag } = useFeatureFlagStore();

  useEffect(() => {
    initGlobal(config);
    initFeatureFlag(featureFlag);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [config, featureFlag]);

  return children;
}

export default ZustandContext;
