const { google } = require('googleapis');
const fs = require('fs');
require('dotenv').config();

async function authenticateGoogle() {
  const credentials = {
    type: 'service_account',
    project_id: process.env.GOOGLE_PROJECT_ID,
    private_key_id: process.env.GOOGLE_PRIVATE_KEY_ID,
    private_key: process.env.GOOGLE_PRIVATE_KEY.replace(/\\n/g, '\n'),
    client_email: process.env.GOOGLE_CLIENT_EMAIL,
    client_id: process.env.GOOGLE_CLIENT_ID,
    auth_uri: 'https://accounts.google.com/o/oauth2/auth',
    token_uri: 'https://oauth2.googleapis.com/token',
    auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',
    client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.GOOGLE_CLIENT_EMAIL}`,
  };

  const auth = new google.auth.GoogleAuth({
    credentials,
    scopes: [
      'https://www.googleapis.com/auth/drive.file',
      'https://www.googleapis.com/auth/spreadsheets.readonly',
    ],
  });

  return auth.getClient();
}

// Convert nested keys into JSON structure.
function setNestedValue(obj, keyParts, value) {
  keyParts.reduce((acc, part, index) => {
    if (index === keyParts.length - 1) {
      acc[part] = value;
    } else {
      acc[part] = acc[part] || {};
    }
    return acc[part];
  }, obj);
}

// Process data from a single sheet.
function processSheetData(sheetName, data) {
  if (!data || data.length === 0) {
    console.warn(`No data found in sheet: ${sheetName}`);
    return { en: {}, zh: {} };
  }

  const headers = data[0];
  const enData = {};
  const zhData = {};

  data.slice(1).forEach((row) => {
    const rowData = {};

    headers.forEach((header, index) => {
      let cellValue = row[index];

      // Handle booleans explicitly
      if (cellValue === 'TRUE') cellValue = true;
      if (cellValue === 'FALSE') cellValue = false;

      rowData[header] = cellValue;
    });

    const { Key: key, EN: enValue, ZH: zhValue } = rowData;

    if (key) {
      const keyParts = key.split('.');
      setNestedValue(enData, keyParts, enValue);
      setNestedValue(zhData, keyParts, zhValue);
    }
  });

  return { en: enData, zh: zhData };
}

async function googleSheetToJson(
  spreadsheetId,
  outputEnJsonPath,
  outputZhJsonPath,
) {
  const auth = await authenticateGoogle();
  const drive = google.drive({ version: 'v3', auth });
  const sheets = google.sheets({ version: 'v4', auth });

  try {
    const fileDetails = await drive.files.get({
      fileId: spreadsheetId,
      fields: 'name, mimeType',
    });
    console.log(`File Name: ${fileDetails.data.name}`);
    console.log(`File Type: ${fileDetails.data.mimeType}`);

    let googleSheetId = spreadsheetId;

    // Convert Excel file to Google Sheets if necessary
    if (
      fileDetails.data.mimeType ===
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ) {
      const response = await drive.files.copy({
        fileId: spreadsheetId,
        requestBody: {
          name: `${fileDetails.data.name} - Converted`,
          mimeType: 'application/vnd.google-apps.spreadsheet',
        },
      });
      googleSheetId = response.data.id;

      console.log('Converting Google Sheet data to JSON files...');
    }

    // Fetch metadata for all sheets
    const sheetMetadata = await sheets.spreadsheets.get({
      spreadsheetId: googleSheetId,
    });

    const enData = {};
    const zhData = {};

    // Process each sheet
    for (const sheet of sheetMetadata.data.sheets) {
      const sheetName = sheet.properties.title;

      const sheetData = await sheets.spreadsheets.values.get({
        spreadsheetId: googleSheetId,
        range: `${sheetName}!A1:Z1000`,
      });

      const { en, zh } = processSheetData(sheetName, sheetData.data.values);
      enData[sheetName] = en;
      zhData[sheetName] = zh;
    }

    fs.writeFileSync(
      outputEnJsonPath,
      JSON.stringify(enData, null, 2),
      'utf-8',
    );

    fs.writeFileSync(
      outputZhJsonPath,
      JSON.stringify(zhData, null, 2),
      'utf-8',
    );

    console.log(
      'JSON file successfully saved in:',
      outputEnJsonPath,
      outputZhJsonPath,
    );
  } catch (error) {
    console.error(
      'Error processing Google Sheet data:',
      error.response?.data || error.message,
    );
  }
}

//spreadsheetId can be obtained from Google Sheet Url
//(e.g. https://docs.google.com/spreadsheets/d/SPREADSHEET_ID/edit)
const spreadsheetId = '1ErDAR1c3NYkRRkzH0SHv8EX7p7uvMfsD'; // Replace with your SPREADSHEET_ID if necessary

const outputEnJsonPath = '../src/dictionaries/en.json';
const outputZhJsonPath = '../src/dictionaries/zh.json';

googleSheetToJson(spreadsheetId, outputEnJsonPath, outputZhJsonPath);
