locals {
  project_name = "baymax"
  service_name = "hiseven"

  vpc_id      = "vpc-t4n9ihty6mingigr2i0b0"                                #nt
  vswitch_ids = ["vsw-t4nl9tm2bpdo5pk23tvmo", "vsw-t4n1lkud2wldpnrx5rgfq"] #nt vswitch

  staging_listener_id    = "lsn-7gm21lee352lku40s1"
  production_listener_id = "lsn-oov1a3bla6xe0dizrf"
  cpu                    = 2
  memory                 = 4
  port                   = "3000"

  domain_suffix = terraform.workspace == "production" ? "" : "-staging"

  tags = {
    Name        = local.project_name
    Environment = terraform.workspace
    Workspace   = terraform.workspace
    Project     = local.project_name
    Terraform   = "true"
    Path        = "MyDev-Baymax-Web"
  }
}

resource "alicloud_security_group" "this" {
  vpc_id = local.vpc_id
  name   = "${local.project_name}-${local.service_name}-${terraform.workspace}-eci-sg"

  resource_group_id = var.resource_group_id

  tags = local.tags
}

resource "alicloud_ess_scaling_group" "this" {
  min_size                  = 3
  max_size                  = 6
  scaling_group_name        = "${local.project_name}-${local.service_name}-${terraform.workspace}-ess"
  removal_policies          = ["OldestScalingConfiguration", "OldestInstance"]
  vswitch_ids               = local.vswitch_ids
  group_type                = "ECI"
  group_deletion_protection = true

  multi_az_policy = "BALANCE"
  az_balance      = "true"

  resource_group_id = var.resource_group_id

  lifecycle {
    ignore_changes = [alb_server_group]
  }

  tags = local.tags
}

resource "alicloud_ess_eci_scaling_configuration" "this" {
  scaling_configuration_name = "${local.project_name}-${local.service_name}-${terraform.workspace}-sc"
  scaling_group_id           = alicloud_ess_scaling_group.this.id
  cpu                        = local.cpu
  memory                     = local.memory
  security_group_id          = alicloud_security_group.this.id
  spot_strategy              = "SpotAsPriceGo"
  force_delete               = true
  active                     = true
  container_group_name       = "${local.project_name}-${local.service_name}-${terraform.workspace}-cg"
  containers {
    image             = "hiseven-registry-vpc.ap-southeast-1.cr.aliyuncs.com/${local.project_name}/${local.service_name}:${var.image_tag}"
    name              = "${local.project_name}-${local.service_name}-${terraform.workspace}"
    cpu               = local.cpu
    memory            = local.memory
    image_pull_policy = "IfNotPresent"

    ports {
      port     = local.port
      protocol = "TCP"
    }

    environment_vars {
      key   = "SERVICE_NAME"
      value = var.service_name
    }

    # liveness_probe_failure_threshold     = 3
    # liveness_probe_initial_delay_seconds = 0
    # liveness_probe_period_seconds        = 10
    # liveness_probe_success_threshold     = 1
    # liveness_probe_timeout_seconds       = 1
    # liveness_probe_http_get_path         = "health-check/liveness"
    # liveness_probe_http_get_port         = 3000
    # liveness_probe_http_get_scheme       = "HTTP"

    # readiness_probe_failure_threshold     = 3
    # readiness_probe_initial_delay_seconds = 5
    # readiness_probe_period_seconds        = 10
    # readiness_probe_success_threshold     = 1
    # readiness_probe_timeout_seconds       = 5
    # readiness_probe_http_get_path         = "health-check/readiness"
    # readiness_probe_http_get_port         = 3000
    # readiness_probe_http_get_scheme       = "HTTP"
  }

  resource_group_id = var.resource_group_id

  tags = local.tags
}

resource "alicloud_ess_scaling_rule" "this" {
  scaling_group_id  = alicloud_ess_scaling_group.this.id
  scaling_rule_type = "TargetTrackingScalingRule"
  metric_name       = "EciPodCpuUtilization"
  target_value      = 70
}

#####################
## ALB Server Group
#####################
resource "alicloud_alb_server_group" "this" {
  server_group_name = "${local.project_name}-${local.service_name}-${terraform.workspace}-alb-server-group"
  server_group_type = "Instance"
  vpc_id            = local.vpc_id
  health_check_config {
    health_check_enabled = "false"
  }
  sticky_session_config {
    sticky_session_enabled = false
  }

  resource_group_id = var.resource_group_id

  lifecycle {
    ignore_changes = [servers]
  }

  tags = local.tags
}

resource "alicloud_ess_alb_server_group_attachment" "this" {
  scaling_group_id    = alicloud_ess_eci_scaling_configuration.this.scaling_group_id
  alb_server_group_id = alicloud_alb_server_group.this.id
  port                = 3000
  weight              = 100
  force_attach        = true
}

resource "random_integer" "priority" {
  min = 1
  max = 10000
  keepers = {
    # Generate a new integer each time we switch to a new listener id
    listener_id = terraform.workspace == "production" ? local.production_listener_id : local.staging_listener_id
  }
}

resource "alicloud_alb_rule" "this" {
  rule_name   = "${local.project_name}-${local.service_name}-${terraform.workspace}-alb-rule"
  listener_id = random_integer.priority.keepers.listener_id
  priority    = random_integer.priority.result
  rule_conditions {
    host_config {
      values = ["www${local.domain_suffix}.hiseven.com", "www-v1${local.domain_suffix}.hiseven.com"]
    }
    type = "Host"
  }

  rule_actions {
    forward_group_config {
      server_group_tuples {
        server_group_id = alicloud_alb_server_group.this.id
      }
    }
    order = "9"
    type  = "ForwardGroup"
  }
}
