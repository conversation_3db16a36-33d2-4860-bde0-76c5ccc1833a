'use client';

import DropDown from '@hi7/assets/icon/chevron-down-white.svg';
import type { MenuLinkProps } from '@hi7/interface/link';
import clsx from 'clsx';
import { usePathname } from 'next/navigation';
import { useRef, useState } from 'react';
import Link from '../Link';

const SubMenuLink = ({
  url,
  children,

  items = [],
}: OmitStrict<MenuLinkProps, 'asButton'>) => {
  const pathname = usePathname();
  const hasSubitem = items.length > 0;

  const [isOpen, setIsOpen] = useState(false);
  const clearTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  function handleMouseEnter() {
    clearTimeoutRef.current && clearTimeout(clearTimeoutRef.current);
    setIsOpen(true);
  }

  function handleMouseLeave() {
    clearTimeoutRef.current && clearTimeout(clearTimeoutRef.current);
    clearTimeoutRef.current = setTimeout(() => {
      setIsOpen(false);
    }, 500);
  }

  return (
    <div
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      className={clsx(
        pathname === url || pathname.includes(url) ? 'text-hi7-primary' : '',
        'relative flex cursor-pointer items-center gap-2.5',
      )}
    >
      {children}

      {hasSubitem && (
        <>
          <DropDown />

          {isOpen && (
            <div
              className={clsx(
                'rounded-[10px absolute top-full left-0 z-50 mt-7 h-fit min-h-fit w-fit overflow-hidden',
              )}
              onMouseEnter={handleMouseEnter}
              onMouseLeave={handleMouseLeave}
            >
              {items.map(({ url, text, icon }) => (
                <Link
                  url={url}
                  key={url}
                  className={clsx(
                    'flex items-center gap-2 whitespace-nowrap',
                    'cursor-pointer text-start text-black',
                    'px-[14px] py-4',
                    'transition-all duration-300',
                    'mb-2 w-full min-w-[155px] cursor-pointer rounded-[5px] bg-white/95 hover:bg-[#C7E5FF]',
                  )}
                >
                  <i className="text-[#172DB1]">{icon}</i>
                  {text}
                </Link>
              ))}
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default SubMenuLink;
