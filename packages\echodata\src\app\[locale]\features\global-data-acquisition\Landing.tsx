

import BannerLongRoundVector from '@hi7/assets/background/elongated-round-vector.svg';
import BannerBgImg from '@hi7/assets/background/global-accqui-bg-img.png';
import BannerBgImgMobile from '@hi7/assets/background/global-accqui-bg-mobile-img.png';
import BannerRoundVector from '@hi7/assets/background/rounded-vector.svg';
import type { DictionaryProps } from '@hi7/interface/i18n';
import { Arsenal } from 'next/font/google';
import Image from 'next/image';

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
  display: 'swap',
});

function Landing({ dictionary }: DictionaryProps) {

  return (
    <div className="relative flex items-center justify-center overflow-hidden">
      <div className="w-full">
        <div className="h-screen md:h-[50vh] lg:h-[90vh]">

          <div className="lg:flex w-full items-center justify-center">
            <Image
              fill
              src={BannerBgImg}
              alt={'background'}
              className="hidden md:block"
            />
            <Image
              src={BannerBgImgMobile}
              alt={'background'}
              className="object-cover md:hidden w-full h-full"
            />
            <div className="w-[90%] absolute left-10 top-10 z-10 text-white text-start whitespace-break-spaces transition-opacity duration-700 xl:left-24 xl:top-25 ">
              <h1 className={`text-[48px] font-bold leading-[50px] tracking-[1px] lg:text-[64px] xl:text-[77px] xl:leading-[68px] xl:mb-9 ${arsenal.className}`}>{dictionary.features.globalDataAcquisition.banner.title}</h1>
              <hr className='w-[90%] my-4 md:mx-auto md:w-full lg:my-5 xl:my-8' />
              <div className="grid grid-cols-[30%_70%] gap-2 items-center lg:grid-cols-[20%_80%] lg:gap-5 lg:pl-5">
                <div className="text-right items-center justify-center self-center">
                  <p className='font-[400] text-[14px] leading-[20px] lg:text-[20px] xl:text-[29px] xl:leading-[30px]'>{dictionary.features.globalDataAcquisition.banner.point1Title}</p>
                </div>
                <h3 className='mb-0 flex flex-col md:flex-row items-baseline'>
                  <span className="font-bold text-[28px] leading-[35px] lg:text-[48px]">200+</span>
                  <span className="font-thin text-[20px] leading-[20px] lg:text-[48px]"> {dictionary.features.globalDataAcquisition.banner.point1Desc1}</span>
                  <span className="font-thin text-[12px] leading-[20px] lg:text-[20px]"> {dictionary.features.globalDataAcquisition.banner.point1Desc2}</span>
                </h3>
              </div>
              <hr className='w-[90%] my-4 md:mx-auto md:w-full lg:my-5 xl:my-8' />
              <div className="grid grid-cols-[30%_70%] gap-2 items-center lg:grid-cols-[20%_80%] lg:gap-5 lg:pl-5">
                <div className="text-right items-center justify-center self-center">
                  <p className='font-[400] text-[14px] leading-[20px] lg:text-[20px] xl:text-[29px] xl:leading-[30px]'>{dictionary.features.globalDataAcquisition.banner.point2Title}</p>
                </div>
                <h3 className='mb-0 flex flex-col md:flex-row items-baseline'>
                  <span className="font-bold text-[28px] leading-[35px] lg:text-[48px]"> {dictionary.features.globalDataAcquisition.banner.point2Desc1}</span>
                  <span className="font-thin text-[20px] leading-[20px] lg:text-[48px]"> {dictionary.features.globalDataAcquisition.banner.point2Desc2}</span>
                  <span className="font-thin text-[12px] leading-[20px] lg:text-[20px]"> {dictionary.features.globalDataAcquisition.banner.point2Desc3}</span>
                </h3>
              </div>
              <hr className='w-[90%] my-4 md:mx-auto md:w-full lg:my-5 xl:my-8' />
              <div className="grid grid-cols-[30%_70%] gap-2 items-center lg:grid-cols-[20%_80%] lg:gap-5 lg:pl-5">
                <div className="text-right items-center justify-center self-center">
                  <p className='font-[400] text-[14px] leading-[20px] lg:text-[20px] xl:text-[29px] xl:leading-[30px]'>{dictionary.features.globalDataAcquisition.banner.point3Title}</p>
                </div>
                <h3 className='mb-0 flex flex-col md:flex-row items-baseline'>
                  <span className="font-bold text-[28px] leading-[35px] lg:text-[48px]"> {dictionary.features.globalDataAcquisition.banner.point3Desc1}</span>
                  <span className="font-thin text-[20px] leading-[20px] lg:text-[48px]"> {dictionary.features.globalDataAcquisition.banner.point3Desc2}</span>
                </h3>
              </div>
              <hr className='w-[100%] my-4 md:mx-auto md:w-full' />
              <div className="block lg:flex">
                <a
                  href="#"
                  target="_blank"
                  className="mt-5 block w-auto max-w-[200px] text-center cursor-pointer rounded-[25px] bg-[#FF5542] py-1 px-[20px] whitespace-nowrap text-white hover:bg-[#C7E5FF] font-bold text-[18px] leading-[40px]"
                >
                  {dictionary.general.freeTrial.button1}
                </a>
                <a
                  href="https://scrmchampion.com/contact"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="mt-5 block w-auto max-w-[200px] text-center cursor-pointer rounded-[25px] bg-[#047AFF] py-1 px-[20px] whitespace-nowrap text-white hover:bg-[#C7E5FF] font-bold text-[18px] leading-[40px] lg:ml-5"
                >
                  {dictionary.general.freeTrial.button5}
                </a>
              </div>
            </div>
            <BannerLongRoundVector className="absolute scale-55 -right-43 -bottom-13 md:hidden" />
            <BannerRoundVector className="absolute scale-55 right-16 bottom-10 md:hidden" />
          </div>
        </div>
      </div>
    </div >
  );
}

export default Landing;
