.embla {
  --slide-height: 19rem;
  --slide-spacing: 1rem;
  --slide-size: 80%;
  cursor: grab;
}

.embla__home {
  --slide-height: 19rem;
  --slide-spacing: 1rem;
  --slide-size: 340px;
  cursor: grab;
}

.embla__service {
  --slide-size: 350px;
}

.embla__viewport {
  overflow: hidden;
  width: 100vw;
  transform: translate(-15px, -25px);
}

@media screen and (min-width: 1024px) {
  .embla__viewport {
    transform: translate(-120px, -25px);
  }

  .embla {
    --slide-size: 25%;
  }
}

.embla__container {
  display: flex;
  backface-visibility: hidden;
  touch-action: pan-y pinch-zoom;
  margin-left: calc(var(--slide-spacing) * -1);
}

.embla__slide {
  transform: translate3d(0, 0, 0);
  flex: 0 0 var(--slide-size);
  min-width: 0;
  padding-left: var(--slide-spacing);
}

.embla__slide__number {
  box-shadow: '0px 5px 30px 0px rgba(0, 0, 0, 0.10)';
  border-radius: 1.8rem;
  font-size: 4rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  height: var(--slide-height);
  user-select: none;
}
