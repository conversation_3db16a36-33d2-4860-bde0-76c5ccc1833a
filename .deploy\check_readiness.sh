#!/bin/bash
SERVICE=$1
DOMAIN_SURFFIX=""
ENVIRONMENT="production"

echo "$SERVICE $DOMAIN_SURFFIX"
# JSON string
json=$(curl -s "https://$SERVICE$DOMAIN_SURFFIX.hiseven.com/health-check/readiness")
version=$(echo "$json" | jq -r '.version')
echo "$version"

start_time=$(date +%s)
timeout=$((10 * 60)) # 10 minutes in seconds
success_count=0

while [ $success_count -lt 3 ]; do
  current_time=$(date +%s)
  elapsed_time=$((current_time - start_time))
  sleep 10 # Sleep for a short period to avoid tight loop

  # Check if the version is v1.0.1
  if [ "$ENVIRONMENT" == "production" ] && [ "$version" == "$CI_COMMIT_TAG" ]; then
    success_count=$((success_count + 1))
    echo "Production Probe Success $success_count time(s)"
  elif
    [ "$ENVIRONMENT" == "staging" ] && [ "$version" == "$CI_COMMIT_SHORT_SHA" ]
  then
    success_count=$((success_count + 1))
    echo "Staging Probe Success $success_count time(s)"
  fi
  if [ "$elapsed_time" -ge "$timeout" ]; then
    echo "Error: One or more containers still running on old image after $timeout seconds of validation"
    exit 1
  fi
done

echo "Rolling Update Success"
