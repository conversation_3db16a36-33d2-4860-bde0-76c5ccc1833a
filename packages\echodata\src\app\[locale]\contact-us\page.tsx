import ProductBgTopLeft from '@hi7/assets/background/contact-bg-top-left.svg';
import ProductBgTopRight from '@hi7/assets/background/contact-bg-top-right.svg';
import Product1 from '@hi7/assets/icon/contact-1.svg';
import Product2 from '@hi7/assets/icon/contact-2.svg';
import Product3 from '@hi7/assets/icon/contact-3.svg';
import Facebook from '@hi7/assets/icon/contact-fb.svg';
import Instagram from '@hi7/assets/icon/contact-ig.svg';
import LinkedIn from '@hi7/assets/icon/contact-in.svg';
import X from '@hi7/assets/icon/contact-x.svg';
import h1 from '@hi7/assets/logo/h1.png';
import h11 from '@hi7/assets/logo/h11.png';
import h12 from '@hi7/assets/logo/h12.png';
import h15 from '@hi7/assets/logo/h15.png';
import h2 from '@hi7/assets/logo/h2.png';
import h3 from '@hi7/assets/logo/h3.png';
import h4 from '@hi7/assets/logo/h4.png';
import h6 from '@hi7/assets/logo/h6.png';
import h7 from '@hi7/assets/logo/h7.png';
import h8 from '@hi7/assets/logo/h8.png';
import h9 from '@hi7/assets/logo/h9.png';
import Image from 'next/image';

import TriggerAnimation from '@hi7/components/TriggerAnimation';
import type { Dictionary } from '@hi7/interface/dictionary';
import { LocaleProps } from '@hi7/interface/i18n';
import { getDictionary } from '@hi7/lib/i18n';
import Link from 'next/link';
import GetStarted from './GetStarted';

const medias = [
  {
    icon: <Facebook />,
    url: 'https://www.facebook.com/profile.php?id=61566180207939',
    target: '_blank',
  },
  {
    icon: <Instagram />,
    url: 'https://www.instagram.com/hisevendm/',
    target: '_blank',
  },
  {
    icon: <X />,
    url: 'https://www.x.com/hisevendm/',
    target: '_blank',
  },
  {
    icon: <LinkedIn />,
    url: 'https://my.linkedin.com/company/hiseven?trk=public_post_feed-actor-name',
    target: '_blank',
  },
];

async function page({ params }: LocaleProps) {
  const { locale } = params;
  const dictionary: Dictionary = await getDictionary(locale);
  return (
    <>
      <div className="flex items-center justify-center overflow-hidden bg-linear-[180deg,#172DB1_0%,#90C6FF_50%,#E1EFFF_100%]">
        <div className="w-full">
          <div className="relative lg:min-h-[700px]">
            <div className="lg:animate-contact-top-ring-left absolute top-[-90px] left-[-240px] w-full lg:top-[15px] lg:left-0 lg:w-[350px]">
              <ProductBgTopLeft width="100%" />
            </div>
            <div className="lg:animate-contact-top-ring-right absolute top-[-90px] right-0 w-[55px] lg:top-[5%] lg:w-[350px]">
              <ProductBgTopRight width="100%" />
            </div>

            <div className="absolute top-0 right-0 left-0 m-auto hidden max-w-[1920px] lg:block">
              <Image
                width={60}
                height={60}
                src={h1}
                alt={'Whatsapp'}
                className="lg:animate-contact-top-icons-set-1 absolute top-[440px] left-[25%] rounded-full"
              />
              <Image
                width={64}
                height={64}
                src={h2}
                alt={'Telegram'}
                className="lg:animate-contact-top-icons-set-2 absolute top-[440px] left-[12.5%] rounded-full" // 120/1920
              />
              <Image
                width={76}
                height={76}
                src={h3}
                alt={'Line'}
                className="lg:animate-contact-top-icons-set-1 absolute top-[120px] left-[20%] rounded-full" // 170/1920
              />
              <Image
                width={88}
                height={88}
                src={h4}
                alt={'L'}
                className="lg:animate-contact-top-icons-set-3 absolute top-[240px] right-[23%] rounded-full" // 240/1920
              />
              <Image
                width={34}
                height={34}
                src={h6}
                alt={'FB Messager'}
                className="lg:animate-contact-top-icons-set-2 absolute top-[280px] left-[25%] rounded-full" // 300/1920
              />
              <Image
                width={30}
                height={30}
                src={h7}
                alt={'Meta'}
                className="lg:animate-contact-top-icons-set-4 absolute top-[340px] right-[12.5%] rounded-full" // 140/1920
              />
              <Image
                width={64}
                height={64}
                src={h8}
                alt={'X'}
                className="lg:animate-contact-top-icons-set-1 absolute top-[160px] left-[30%] rounded-full" // 420/1920
              />
              <Image
                width={36}
                height={36}
                src={h9}
                alt={'Instagram'}
                className="lg:animate-contact-top-icons-set-2 absolute top-[260px] left-[10%] rounded-full" // 80/1920
              />

              <Image
                width={30}
                height={30}
                src={h11}
                alt={'Skype'}
                className="lg:animate-contact-top-icons-set-4 absolute top-[140px] right-[15%] rounded-full" // 180/1920
              />
              <Image
                width={82}
                height={82}
                src={h12}
                alt={'Discord'}
                className="lg:animate-contact-top-icons-set-3 absolute top-[100px] right-[30%] rounded-full" // 380/1920
              />
              <Image
                width={30}
                height={30}
                src={h15}
                alt={'H'}
                className="lg:animate-contact-top-icons-set-4 absolute top-[420px] right-[27.5%] rounded-full" // 400/1920
              />
            </div>

            <div className="relative flex flex-col items-center justify-center pt-16 text-white lg:pt-[180px]">
              <div className="flex max-w-[320px] flex-col text-center lg:max-w-[480px]">
                <h1 className="lg:animate-contact-top-title mb-5 text-[40px] leading-[48px] font-bold lg:text-[50px] lg:leading-[55px] lg:opacity-0">
                  {dictionary.contactUs.landing.title1} <br />{' '}
                  {dictionary.contactUs.landing.title2}
                </h1>

                <h3 className="lg:animate-contact-top-title text-[20px] leading-7 lg:opacity-0">
                  {dictionary.contactUs.landing.desc}
                </h3>

                <div className="lg:animate-contact-top-content mt-4 mb-7 hidden auto-rows-[36px] grid-cols-[36px_36px_36px_36px] items-center justify-center gap-4 lg:grid lg:opacity-0">
                  {medias.map((media, index) => (
                    <Link key={index} href={media.url} target={media.target}>
                      <li className="flex items-center">{media.icon}</li>
                    </Link>
                  ))}
                </div>

                <div className="lg:animate-contact-top-content mt-5 mb-[115px] flex flex-col items-center justify-center lg:m-0 lg:opacity-0">
                  <a
                    href="https://scrmchampion.com/contact"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block w-max cursor-pointer rounded-[50px] bg-[#F5CC00] px-6 py-3 whitespace-nowrap text-[#1E1E1E] hover:bg-[#C7E5FF]"
                  >
                    {dictionary.contactUs.landing.button}
                  </a>
                </div>
              </div>
            </div>
          </div>
          <TriggerAnimation>
            <div className="relative flex flex-col items-center lg:min-h-[560px]">
              <h1 className="lg:animate-contact-bottom-title mb-16 max-w-[340px] text-center text-[32px] leading-[38px] font-bold text-white lg:max-w-[900px] lg:text-[50px] lg:leading-[55px] lg:text-black lg:opacity-0">
                {dictionary.contactUs.feature.title}
              </h1>
              <div className="grid w-full auto-rows-[190px] gap-x-[60px] gap-y-[30px] px-5 pb-10 lg:grid-cols-3 lg:px-30">
                <div className="lg:animate-contact-bottom-0 flex flex-row items-center justify-center gap-2.5 rounded-[30px] bg-linear-[104deg,#E9F0FF_3.49%,#FFF_36.67%,#FFF_57.93%,#FFF_62.08%,#D7E4FF_107.19%] p-5 shadow-[0_0_10px_0px_rgba(0,0,0,0.11)] lg:translate-x-[-100%] lg:opacity-0">
                  <Product1 className="max-w-[85px]" />
                  <div className="flex flex-col gap-2.5">
                    <b className="text-[18px] leading-[25px]">
                      {
                        dictionary.contactUs.feature.officialCustomerServiceTg
                          .title
                      }
                    </b>
                    <p className="">
                      {
                        dictionary.contactUs.feature.officialCustomerServiceTg
                          .desc
                      }
                    </p>
                    <a
                      className="text-[#1093FF]"
                      href="https://007tg.com/ccs/champions"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {
                        dictionary.contactUs.feature.officialCustomerServiceTg
                          .telegram
                      }
                    </a>
                  </div>
                </div>

                <div className="lg:animate-contact-bottom-1 flex flex-row items-center justify-center gap-2.5 rounded-[30px] bg-linear-[104deg,#E9F0FF_3.49%,#FFF_36.67%,#FFF_57.93%,#FFF_62.08%,#D7E4FF_107.19%] p-5 shadow-[0_0_10px_0px_rgba(0,0,0,0.11)] lg:translate-y-[100%] lg:opacity-0">
                  <Product2 className="max-w-[85px]" />
                  <div className="flex flex-col gap-2.5">
                    <b className="text-[18px] leading-[25px]">
                      {
                        dictionary.contactUs.feature.officialCustomerServiceWs
                          .title
                      }
                    </b>
                    <p className="">
                      {
                        dictionary.contactUs.feature.officialCustomerServiceWs
                          .desc
                      }
                    </p>
                    <a
                      className="text-[#1093FF]"
                      href={dictionary.footer.customerService.url}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {
                        dictionary.contactUs.feature.officialCustomerServiceWs
                          .whatsapp
                      }
                    </a>
                  </div>
                </div>

                <div className="lg:animate-contact-bottom-2 flex flex-row items-center justify-center gap-2.5 rounded-[30px] bg-linear-[104deg,#E9F0FF_3.49%,#FFF_36.67%,#FFF_57.93%,#FFF_62.08%,#D7E4FF_107.19%] p-5 shadow-[0_0_10px_0px_rgba(0,0,0,0.11)] lg:translate-x-[100%] lg:opacity-0">
                  <Product3 className="max-w-[85px]" />
                  <div className="flex flex-col gap-2.5">
                    <b className="text-[18px] leading-[25px]">
                      {
                        dictionary.contactUs.feature.officialCustomerServiceEm
                          .title
                      }
                    </b>
                    <p className="">
                      {
                        dictionary.contactUs.feature.officialCustomerServiceEm
                          .desc
                      }
                    </p>
                    <a
                      className="text-[#1093FF]"
                      href="mailto:<EMAIL>"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {
                        dictionary.contactUs.feature.officialCustomerServiceEm
                          .supportChuchai
                      }
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </TriggerAnimation>
        </div>
      </div>
      <GetStarted dictionary={dictionary} locale={locale} />
    </>
  );
}

export default page;
