'use client';

import DropDown from '@hi7/assets/icon/chevron-down-white.svg';
import type { MenuLinkProps } from '@hi7/interface/link';
import clsx from 'clsx';
import { usePathname } from 'next/navigation';
import { useState } from 'react';
import Link from '../Link';

const SubMenuLinkMobile = ({
  url,
  children,
  onClick,

  items = [],
}: OmitStrict<MenuLinkProps, 'asButton'>) => {
  const pathname = usePathname();
  const hasSubitem = items.length > 0;

  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <div
        onClick={() => setIsOpen((prev) => !prev)}
        className={clsx(
          pathname === url || pathname.includes(url) ? 'text-hi7-primary' : '',
          'flex cursor-pointer items-center gap-2.5',
          'py-4',
        )}
      >
        {children}
        <DropDown />
      </div>

      {hasSubitem && (
        <>
          {isOpen && (
            <div>
              {items.map(({ url, text, icon }) => (
                <Link
                  key={url}
                  url={url}
                  onClick={onClick}
                  className={clsx(
                    'flex items-center gap-2 whitespace-nowrap',
                    'cursor-pointer',
                    'py-4',
                  )}
                >
                  {icon}
                  {text}
                </Link>
              ))}
            </div>
          )}
        </>
      )}
    </>
  );
};

export default SubMenuLinkMobile;
