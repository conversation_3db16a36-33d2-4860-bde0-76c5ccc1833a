:root {
  --help-animation-duration: 1s;
  --help-animation-half-duration: 0.5s;
}

@theme {
  --animate-help-top-ring-left: helpTopRingLeft var(--help-animation-duration)
    ease-in-out forwards;
  @keyframes helpTopRingLeft {
    0% {
      opacity: 0;
      transform: translateX(-235px);
    }

    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }

  --animate-help-top-ring-right: helpTopRingRight var(--help-animation-duration)
    ease-in-out forwards;
  @keyframes helpTopRingRight {
    0% {
      opacity: 0;
      transform: translateX(210px);
    }

    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }

  --animate-help-title: helpTitle var(--help-animation-duration) ease-in-out
    forwards;
  @keyframes helpTitle {
    0% {
      opacity: 0;
      transform: translateY(-50px);
    }

    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  --animate-help-content: helpContent var(--help-animation-duration) ease-in-out
    forwards;
  @keyframes helpContent {
    0% {
      opacity: 0;
      transform: translateY(50px);
    }

    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }
}
