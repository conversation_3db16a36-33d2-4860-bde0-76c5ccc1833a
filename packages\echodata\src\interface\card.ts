import type React from 'react';
import type { Dictionary } from './dictionary';
import type { DictionaryProps } from './i18n';

export type MissionCardProps = {
  icon: React.ReactNode;
  title: string;
  description: string;
};

export type ServiceCardProps = {
  logo: React.ReactNode;
  title: string;
  className?: string;
};

export type ProductCardProps = {
  url: string;
  logo: React.ReactNode;
  hoverLogo?: React.ReactNode;
  dictionary?: Dictionary;
  description: string;
};

export type FeatureCardProps = {
  icon: React.ReactNode;
  label: string;
};

export type LocationCardProps = {
  icon: React.ReactNode;
  country: string;
  description: string;
};

export type OfficeCardProps = {
  country: string;
  email: string;
  phoneNumber: string;
  address?: string;
  contact?: string;
  displayAddress: string;
  phoneNumberOnClick?: () => void;
};

export type PostCardProps = {
  url: string;
  image: React.ReactNode;
  tags: React.ReactNode[];
  publishedDate: string;
  title: string;
  description?: string | React.ReactNode;
  expand?: boolean;
  direction?: 'top' | 'left';
  dictionary?: DictionaryProps['dictionary'];
};
