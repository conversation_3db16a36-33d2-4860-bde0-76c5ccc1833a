:root {
  --home-animation-duration: 1.5s;
  --home-animation-half-duration: 0.75s;
}

@theme {
  --animate-home-bg-left: homeBgLeft var(--home-animation-duration) ease-in-out
    forwards;
  @keyframes homeBgLeft {
    0% {
      transform: translateX(0);
    }

    100% {
      transform: translateX(-235px);
    }
  }

  --animate-home-bg-right: homeBgRight var(--home-animation-duration)
    ease-in-out forwards;
  @keyframes homeBgRight {
    0% {
      transform: translateX(0);
    }

    100% {
      transform: translateX(210px);
    }
  }

  --animate-home-bg-ring: homeBgRing var(--home-animation-duration)
    var(--home-animation-half-duration) ease-in-out forwards;
  @keyframes homeBgRing {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }

  --animate-md-home-bg-left: homeMdBgLeft var(--home-animation-duration)
    ease-in-out forwards;
  @keyframes homeMdBgLeft {
    0% {
      transform: translate(0, 0);
    }

    100% {
      transform: translate(-19dvw, -0.25dvh);
    }
  }

  --animate-md-home-bg-right: homeMdBgRight var(--home-animation-duration)
    ease-in-out forwards;
  @keyframes homeMdBgRight {
    0% {
      transform: translate(0, 0);
    }

    100% {
      transform: translate(17dvw, -0.25dvh);
    }
  }

  --animate-home-h1: homeH1 var(--home-animation-duration)
    var(--home-animation-half-duration) ease-in-out forwards;
  @keyframes homeH1 {
    0% {
      transform: translate(50%, 50%);
    }

    100% {
      transform: translate(500px, -440px);
    }
  }

  --animate-home-h2: homeH2 var(--home-animation-duration)
    var(--home-animation-half-duration) ease-in-out forwards;
  @keyframes homeH2 {
    0% {
      transform: translate(50%, 50%);
    }

    100% {
      transform: translate(-435px, -535px);
    }
  }

  --animate-home-h3: homeH3 var(--home-animation-duration)
    var(--home-animation-half-duration) ease-in-out forwards;
  @keyframes homeH3 {
    0% {
      transform: translate(50%, 50%);
    }

    100% {
      transform: translate(-535px, -355px);
    }
  }

  --animate-home-h4: homeH4 var(--home-animation-duration)
    var(--home-animation-half-duration) ease-in-out forwards;
  @keyframes homeH4 {
    0% {
      transform: translate(50%, 50%);
    }

    100% {
      transform: translate(-650px, -455px);
    }
  }

  --animate-home-h6: homeH6 var(--home-animation-duration)
    var(--home-animation-half-duration) ease-in-out forwards;
  @keyframes homeH6 {
    0% {
      transform: translate(50%, 50%);
    }

    100% {
      transform: translate(-450px, -265px);
    }
  }

  --animate-home-h7: homeH7 var(--home-animation-duration)
    var(--home-animation-half-duration) ease-in-out forwards;
  @keyframes homeH7 {
    0% {
      transform: translate(50%, 50%);
    }

    100% {
      transform: translate(480px, -535px);
    }
  }

  --animate-home-h8: homeH8 var(--home-animation-duration)
    var(--home-animation-half-duration) ease-in-out forwards;
  @keyframes homeH8 {
    0% {
      transform: translate(50%, 50%);
    }

    100% {
      transform: translate(440px, -275px);
    }
  }

  --animate-home-h9: homeH9 var(--home-animation-duration)
    var(--home-animation-half-duration) ease-in-out forwards;
  @keyframes homeH9 {
    0% {
      transform: translate(50%, 50%);
    }

    100% {
      transform: translate(640px, -535px);
    }
  }

  --animate-home-h12: homeH12 var(--home-animation-duration)
    var(--home-animation-half-duration) ease-in-out forwards;
  @keyframes homeH12 {
    0% {
      transform: translate(50%, 50%);
    }

    100% {
      transform: translate(600px, -295px);
    }
  }

  --animate-home-statistic-bg: homeStatisticBg var(--home-animation-duration)
    ease-in-out forwards;
  @keyframes homeStatisticBg {
    100% {
      transform: translateY(-40%);
    }
  }

  --animate-home-statistic-text-1: homeStatisticText1
    var(--home-animation-duration) ease-in-out forwards;
  @keyframes homeStatisticText1 {
    0% {
      transform: translateY(0);
    }

    100% {
      transform: translateY(455px);
    }
  }

  --animate-home-statistic-text-2: homeStatisticText2
    var(--home-animation-duration) ease-in-out forwards;
  @keyframes homeStatisticText2 {
    0% {
      transform: translateY(0);
    }

    100% {
      transform: translateY(525px);
    }
  }

  --animate-home-statistic-text-3: homeStatisticText3
    var(--home-animation-duration) ease-in-out forwards;
  @keyframes homeStatisticText3 {
    0% {
      transform: translateY(0);
    }

    100% {
      transform: translateY(325px);
    }
  }

  --animate-home-statistic-text-4: homeStatisticText4
    var(--home-animation-duration) ease-in-out forwards;
  @keyframes homeStatisticText4 {
    0% {
      transform: translateY(0);
    }

    100% {
      transform: translateY(525px);
    }
  }

  --animate-home-statistic-text-5: homeStatisticText5
    var(--home-animation-duration) ease-in-out forwards;
  @keyframes homeStatisticText5 {
    0% {
      transform: translateY(0);
    }

    100% {
      transform: translateY(455px);
    }
  }

  --animate-home-feature-1-title: homeFeature1Title
    var(--home-animation-duration) ease-in-out forwards;
  @keyframes homeFeature1Title {
    0% {
      opacity: 0;
      transform: translateY(-100%);
    }

    100% {
      opacity: 1;
      transform: translateY(100%);
    }
  }

  --animate-home-feature-1-points: homeFeature1Points
    var(--home-animation-duration) ease-in-out forwards;
  @keyframes homeFeature1Points {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }

  --animate-home-feature-1-ring-left: homeFeature1RingLeft
    var(--home-animation-duration) ease-in-out forwards;
  @keyframes homeFeature1RingLeft {
    0% {
      opacity: 0;
      transform: translateX(-100%);
    }

    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }

  --animate-home-feature-1-ring-right: homeFeature1RingRight
    var(--home-animation-duration) ease-in-out forwards;
  @keyframes homeFeature1RingRight {
    0% {
      opacity: 0;
      transform: translateX(100%);
    }

    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }

  --animate-home-feedback-title: homeFeedbackTitle
    var(--home-animation-duration) ease-in-out forwards;
  @keyframes homeFeedbackTitle {
    0% {
      opacity: 0;
      transform: translateY(-100%);
    }

    100% {
      opacity: 1;
      transform: translateY(100%);
    }
  }

  --animate-home-feedback-bg-0-block: homeFeedbackBg0Block
    var(--home-animation-duration) ease-in-out forwards;
  @keyframes homeFeedbackBg0Block {
    0% {
      transform: translateX(0);
    }

    100% {
      transform: translateX(100%);
    }
  }

  --animate-home-feedback-bg-1-block: homeFeedbackBg1Block
    var(--home-animation-duration) ease-in-out forwards;
  @keyframes homeFeedbackBg1Block {
    0% {
      transform: translateY(0);
    }

    100% {
      transform: translateY(120%);
    }
  }

  --animate-home-feedback-bg-2-block: homeFeedbackBg2Block
    var(--home-animation-duration) ease-in-out forwards;
  @keyframes homeFeedbackBg2Block {
    0% {
      transform: translateX(0);
    }

    100% {
      transform: translateX(-100%);
    }
  }

  --animate-home-feedback-block: homeFeedbackBlock
    var(--home-animation-duration) ease-in-out forwards;
  @keyframes homeFeedbackBlock {
    0% {
      opacity: 0;
      transform: translateY(-100%);
    }

    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  --animate-home-help-title: homeHelpTitle var(--home-animation-duration)
    ease-in-out forwards;
  @keyframes homeHelpTitle {
    0% {
      opacity: 0;
      transform: translateY(-100%);
    }

    100% {
      opacity: 1;
      transform: translateY(100%);
    }
  }

  --animate-home-help-0: homeHelp0 var(--home-animation-duration) ease-in-out
    forwards;
  @keyframes homeHelp0 {
    0% {
      opacity: 0;
      transform: translateX(-100%);
    }

    100% {
      opacity: 1;
      transform: translateX(100%);
    }
  }

  --animate-home-help-1: homeHelp1 var(--home-animation-duration) ease-in-out
    forwards;
  @keyframes homeHelp1 {
    0% {
      opacity: 0;
      transform: translateY(100%);
    }

    100% {
      opacity: 1;
      transform: translateY(-100%);
    }
  }

  --animate-home-help-2: homeHelp2 var(--home-animation-duration) ease-in-out
    forwards;
  @keyframes homeHelp2 {
    0% {
      opacity: 0;
      transform: translateX(100%);
    }

    100% {
      opacity: 1;
      transform: translateX(-100%);
    }
  }
}
