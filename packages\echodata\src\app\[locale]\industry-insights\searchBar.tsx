interface SearchBarProps {
  search: string;
  setSearch: (val: string) => void;
  onSearch?: () => void;
}

export default function SearchBar({
  search,
  setSearch,
  onSearch,
}: SearchBarProps) {
  const handleSearch = () => {
    onSearch?.();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <div className="mb-8 flex justify-center py-1">
      <div className="flex w-[90%] items-center gap-3 md:w-full md:max-w-[34%]">
        <button
          onClick={handleSearch}
          className="focus:ring-opacity-50 flex h-12 w-12 items-center justify-center rounded-full bg-white transition-shadow duration-200 hover:shadow-xl focus:ring-2 focus:ring-[#FF5542] focus:outline-none 2xl:h-18 2xl:w-18"
        >
          <svg
            className="h-5 w-5 text-[#FF5542] 2xl:h-8 2xl:w-8"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 22 22"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </button>
        <input
          type="text"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="Search"
          className="focus:ring-opacity-50 flex-1 rounded-full border-0 bg-white px-6 py-3 text-base text-gray-700 placeholder-[#FF5542] focus:ring-2 focus:ring-[#FF5542] focus:outline-none 2xl:text-[26px]"
        />
      </div>
    </div>
  );
}
