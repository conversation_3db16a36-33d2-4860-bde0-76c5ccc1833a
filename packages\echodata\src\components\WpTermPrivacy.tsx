import { replaceTermPrivacy } from '@hi7/helpers/html';
import { getWpPostApi } from '@hi7/helpers/wp-api';

async function WpTermPrivacy({ postId }: { postId: number }) {
  const post = await getWpPostApi(postId);

  if (!post) {
    return null;
  }

  return (
    <div className="px-4 py-17 md:px-18 md:py-25">
      <div className="mx-auto max-w-(--breakpoint-lg)">
        {replaceTermPrivacy(post.content.rendered)}
      </div>
    </div>
  );
}

export default WpTermPrivacy;
