'use client';

import clsx from 'clsx';
import { useState } from 'react';

import CloseButton from '@hi7/assets/icon/close-button.svg';
import Menu from '@hi7/assets/icon/menu.svg';
import Logo from '@hi7/assets/logo/logo.svg';
import Link from '@hi7/components/Link';
import LocaleSwitcher, {
  LocaleSwitcherButton,
} from '@hi7/components/LocaleSwitcher/Mobile';
import MenuLink from '@hi7/components/MenuLink/Mobile';

import type { DictionaryProps } from '@hi7/interface/i18n';
import type { MenuLinkProps } from '@hi7/interface/link';
import P1 from './icons/p1.svg';
import P2 from './icons/p2.svg';
import P3 from './icons/p3.svg';
import P4 from './icons/p4.svg';
import P5 from './icons/p5.svg';
import P6 from './icons/p6.svg';

// type HeaderProps = Pick<i18n, 'dictionary'>;
// { dictionary }: HeaderProps
const HeaderMobile = ({ dictionary }: DictionaryProps) => {
  const [menuOpen, setMenuOpen] = useState(false);
  const [localeOpen, setLocaleOpen] = useState(false);

  const toggleMenu = () => setMenuOpen((prev) => !prev);
  const toggleLocale = () => setLocaleOpen((prev) => !prev);

  const open = menuOpen || localeOpen;

  const closePanel = () => {
    setLocaleOpen(false);
    setMenuOpen(false);
  };

  const ROUTES: MenuLinkProps[] = [
    {
      url: '/products-solutions',
      children: dictionary.header.productSolution.title,
      items: [
        {
          icon: <P1 />,
          url: '/products-solutions/whatsapp-marketing-management',
          text: dictionary.header.productSolution.whatsAppMarketingManagement,
        },
        {
          icon: <P2 />,
          url: '/products-solutions/social-media-platform',
          text: dictionary.header.productSolution.socialMediaPlatforms,
        },
        {
          icon: <P3 />,
          url: '/products-solutions/customer-service-saas-tool',
          text: dictionary.header.productSolution.customerServicesSaasTool,
        },
        {
          icon: <P4 />,
          url: '/products-solutions/customer-management',
          text: dictionary.header.productSolution.customerManagement,
        },
      ],
    },
    {
      url: '/pricing',
      children: dictionary.header.pricing,
    },
    {
      url: '/industry-insights',
      children: dictionary.header.industryInsights,
    },
    {
      url: '/contact-us',
      children: dictionary.header.support.title,
      items: [
        {
          icon: <P5 />,
          url: '/contact-us',
          text: dictionary.header.support.contactUs,
        },
        {
          icon: <P6 />,
          url: '/help-center',
          text: dictionary.header.support.helpCentre,
        },
      ],
    },
    {
      url: '/download',
      children: dictionary.header.download,
    },
    {
      asButton: true,
      url: 'https://admin.scrmchampion.com/',
      children: `${dictionary.header.button.signUp}/${dictionary.header.button.logIn}`,
    },
  ];

  return (
    <>
      <nav
        className={clsx(
          'm-h-14 z-30 transform bg-linear-[124.78deg,#00B6FF_0.64%,#0506DC] px-4 py-2.5 text-white transition-all duration-200',
        )}
      >
        <div className={clsx('flex items-center gap-4')}>
          <Link url={''} onClick={closePanel}>
            <Logo width="100%" height={36} />
          </Link>
          <div className="flex-1" />

          {!open && <LocaleSwitcherButton onClick={toggleLocale} />}
          {!open && <Menu onClick={toggleMenu} />}

          {open && <CloseButton onClick={closePanel} />}
        </div>

        {menuOpen && (
          <div className="flex flex-col px-2 pt-3 pb-7">
            {ROUTES.map((route, index) => (
              <MenuLink
                key={index}
                {...route}
                onClick={() => {
                  toggleMenu();
                  // route.onClick?.();
                }}
              />
            ))}
          </div>
        )}

        {localeOpen && (
          <div className="flex flex-col px-2 pt-3">
            <LocaleSwitcher />
          </div>
        )}
      </nav>
    </>
  );
};

export default HeaderMobile;
