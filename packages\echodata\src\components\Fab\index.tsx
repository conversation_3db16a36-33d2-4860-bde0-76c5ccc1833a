'use client';

import ContactUsButton from '@hi7/assets/icon/contact-us-button.svg';
import ContactUsClose from '@hi7/assets/icon/contact-us-close.svg';
import ContactUsText from '@hi7/assets/icon/contact-us-text.svg';
import ContactUsTG from '@hi7/assets/icon/contact-us-tg.svg';
import ContactUsWS from '@hi7/assets/icon/contact-us-ws.png';
import LatestNews from '@hi7/assets/icon/latest-news.svg';
import { DictionaryProps } from '@hi7/interface/i18n';
import clsx from 'clsx';
import Image from 'next/image';
import { useRef, useState } from 'react';
import Link from '../Link';

function Fab({ dictionary }: DictionaryProps) {
  const [isLatestOpen, setIsLatestOpen] = useState(false);
  const clearLatestTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  function handleLatestMouseEnter() {
    clearLatestTimeoutRef.current &&
      clearTimeout(clearLatestTimeoutRef.current);
    setIsLatestOpen(true);
  }

  function handleLatestMouseLeave() {
    clearLatestTimeoutRef.current &&
      clearTimeout(clearLatestTimeoutRef.current);
    clearLatestTimeoutRef.current = setTimeout(() => {
      setIsLatestOpen(false);
    }, 500);
  }

  const [isContactOpen, setIsContactOpen] = useState(false);
  const clearContactTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  function handleContactMouseEnter() {
    clearContactTimeoutRef.current &&
      clearTimeout(clearContactTimeoutRef.current);
    setIsContactOpen(true);
  }

  function handleContactMouseLeave() {
    clearContactTimeoutRef.current &&
      clearTimeout(clearContactTimeoutRef.current);
    clearContactTimeoutRef.current = setTimeout(() => {
      setIsContactOpen(false);
    }, 500);
  }

  return (
    <>
      <Link
        url="/news/1"
        className={clsx(
          'fixed bottom-[23%] left-0 z-50 h-[48px] overflow-hidden rounded-full lg:left-[65px]',
          isLatestOpen ? 'w-[165px]' : 'w-[48px]',
        )}
        onMouseEnter={handleLatestMouseEnter}
        onMouseLeave={handleLatestMouseLeave}
      >
        {isLatestOpen && (
          <div className="animate-fab-latest-news absolute top-[1px] left-0 h-[48px] w-[165px] rounded-[50px] bg-white pr-4 text-right text-[14px] leading-[48px] font-bold text-[#F5CC00]">
            Lastest News
          </div>
        )}
        <div className="relative flex h-[48px] w-[48px] items-center justify-center rounded-full border-2 border-white bg-[#F5CC00]">
          <LatestNews />
        </div>
      </Link>
      <div
        className="fixed right-0 bottom-[20%] z-50 lg:right-[40px]"
        onMouseEnter={handleContactMouseEnter}
        onMouseLeave={handleContactMouseLeave}
      >
        {isContactOpen && (
          <div className="animate-fab-latest-contact absolute right-0 bottom-[21px]">
            <div className="mb-[-30px] ml-[10px]">
              <ContactUsText />
            </div>

            <a
              className="mb-[-45px] block"
              href="https://007tg.com/ccs/champions"
              target="_blank"
              rel="noopener noreferrer"
            >
              <ContactUsTG />
            </a>

            <a
              className="relative mb-[-45px] block h-[110px] w-[110px]"
              href={dictionary.footer.customerService.url}
              target="_blank"
              rel="noopener noreferrer"
            >
              <Image src={ContactUsWS} alt="Contact Us WhatsApp" fill />
            </a>
            <ContactUsClose />
          </div>
        )}

        <div
          className={clsx(
            'absolute',
            isContactOpen && 'animate-fab-latest-contact-reserve',
          )}
        >
          {!isContactOpen && (
            <div className="absolute right-[18px] bottom-[80px] z-50">
              <ContactUsText />
            </div>
          )}

          <div className="absolute right-0 bottom-0 z-50">
            <ContactUsButton />
          </div>
        </div>
      </div>
    </>
  );
}

export default Fab;
