'use client';

import ProductBgBottomLeft from '@hi7/assets/background/help-bg-bottom-left.svg';
import ProductBgBottomRight from '@hi7/assets/background/help-bg-bottom-right.svg';
import ProductBgMidLeft from '@hi7/assets/background/help-bg-mid-left.svg';
import ProductBgMidRight from '@hi7/assets/background/help-bg-mid-right.svg';
import ProductBgTopLeft from '@hi7/assets/background/help-bg-top-left.svg';
import ProductBgTopRight from '@hi7/assets/background/help-bg-top-right.svg';
import Minus from '@hi7/assets/icon/minus.svg';
import Plus from '@hi7/assets/icon/plus.svg';

import Search from '@hi7/assets/icon/search.svg';
import type { DictionaryProps } from '@hi7/interface/i18n';
import { Locale } from '@hi7/lib/i18n';
import clsx from 'clsx';
import { useState } from 'react';
import { SUGGESTIONS, ThreadKey, THREADS } from './config';

interface HelpListProps extends DictionaryProps {
  locale: Locale;
}

export default function HelpList({ dictionary }: HelpListProps) {
  const [open, setOpen] = useState<Record<string, boolean>>({ faq1: true });
  return (
    <>
      <div className="flex items-center justify-center overflow-hidden bg-[#AEDAFF]">
        <div className="w-full">
          <div className="relative lg:min-h-dvh">
            <div className="lg:animate-help-top-ring-left absolute top-[-140px] left-[-300px] w-[350px] lg:top-[-160px] lg:left-0">
              <ProductBgTopLeft width="100%" />
            </div>
            <div className="lg:animate-help-top-ring-right absolute top-[-90px] right-[-200px] w-[250px] lg:top-[5%] lg:right-0">
              <ProductBgTopRight width="100%" />
            </div>

            <div className="relative flex flex-col pt-16 lg:pt-[180px]">
              <div className="flex flex-col items-center text-center">
                <h1 className="lg:animate-help-title mb-12 max-w-[320px] text-[40px] leading-[48px] font-bold lg:max-w-[650px] lg:text-[50px] lg:leading-[55px] lg:text-[#0506DC]">
                  {dictionary.helpCentre.landing.title}
                </h1>

                <a
                  href="https://help.scrmchampion.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="lg:animate-help-title mb-8 grid h-[50px] w-dvw max-w-[400px] grid-cols-[24px_1fr] items-center gap-2 rounded-[30px] bg-white px-6 lg:max-w-[750px]"
                >
                  <Search />
                  <input
                    className="text-lg text-[#000] outline-0"
                    placeholder={dictionary.helpCentre.landing.searchBar}
                  />
                </a>
                <div className="lg:animate-help-content grid gap-2.5 pb-[46px] lg:max-w-[840px] lg:grid-cols-[90px_1fr]">
                  <div className="pt-2">
                    {dictionary.helpCentre.landing.suggestion.title}:{' '}
                  </div>
                  <div className="flex flex-row flex-wrap justify-center gap-2.5 lg:justify-start">
                    {SUGGESTIONS.map((s) => (
                      <a
                        href="https://help.scrmchampion.com"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="block rounded-[20px] bg-white/57 px-6 py-2"
                      >
                        {
                          dictionary.helpCentre.landing.suggestion.suggestions[
                            s
                          ]
                        }
                      </a>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex items-center justify-center overflow-hidden bg-[#0506DC]">
        <div className="w-full">
          <div className="relative lg:min-h-[640px]">
            <div className="absolute top-[30%] left-0 w-[135px] lg:top-[5%]">
              <ProductBgMidLeft width="100%" />
            </div>
            <div className="absolute top-[60%] right-0 w-[172px] lg:top-[15%]">
              <ProductBgMidRight width="100%" />
            </div>

            <div className="relative flex flex-col pt-16 lg:py-[80px]">
              <div className="flex flex-col items-center">
                <h1 className="mb-12 text-center text-[40px] leading-[48px] font-bold text-white lg:text-[50px] lg:leading-[55px]">
                  {dictionary.helpCentre.feature1.title}
                </h1>

                <div className="grid w-dvw max-w-[840px] gap-2 px-5">
                  {Object.entries(dictionary.helpCentre.feature1.faq).map(
                    ([faqKey, faqValue]) => {
                      const descriptions = Object.entries(faqValue)
                        .filter(([key]) => key.startsWith('desc'))
                        .sort(([a], [b]) => a.localeCompare(b))
                        .map(([, val]) => val);

                      return (
                        <div
                          key={faqKey}
                          className="flex flex-col gap-4 rounded-lg bg-white/90 p-6"
                        >
                          <div
                            className="flex cursor-pointer flex-row items-center"
                            onClick={() => {
                              setOpen((prev) => ({
                                ...prev,
                                [faqKey]: !prev[faqKey],
                              }));
                            }}
                          >
                            <b
                              className={clsx(
                                'flex-1 text-[22px]',
                                open[faqKey] && 'text-[#172DB1]',
                              )}
                            >
                              {faqValue.title}
                            </b>
                            {open[faqKey] === true ? <Minus /> : <Plus />}
                          </div>

                          {open[faqKey] &&
                            (descriptions.length === 1 ? (
                              <p>{descriptions[0]}</p>
                            ) : (
                              <ul className="list-disc pl-5">
                                {descriptions.map((desc, idx) => (
                                  <li key={idx}>{desc}</li>
                                ))}
                              </ul>
                            ))}
                        </div>
                      );
                    },
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex items-center justify-center overflow-hidden bg-[#C7E5FF]">
        <div className="w-full">
          <div className="relative lg:min-h-[640px]">
            <div className="absolute top-[-90px] left-[-80px] w-[124px] lg:top-[5%] lg:left-0">
              <ProductBgBottomLeft width="100%" />
            </div>
            <div className="absolute top-[55%] right-0 w-[83px] lg:top-[15%]">
              <ProductBgBottomRight width="100%" />
            </div>

            <div className="relative flex flex-col pt-16 lg:py-[80px]">
              <div className="flex flex-col items-center">
                <h1 className="mb-12 text-center text-[40px] leading-[48px] font-bold lg:text-[50px] lg:leading-[55px]">
                  {dictionary.helpCentre.feature2.title}
                </h1>

                <div className="w-dvw max-w-[1140px] px-5">
                  <div className="mb-5 flex flex-col lg:flex-row">
                    <b className="text-2xl text-[#062436]">
                      {dictionary.helpCentre.feature2.recentlyUpdated}
                    </b>
                    <div className="flex-1"></div>
                    <a
                      href="https://bettyes-organization.gitbook.io/scrm/learn-more/release-notes"
                      className="text-[20px] text-[#100B69]"
                    >
                      {dictionary.helpCentre.feature2.moreUpdates}
                    </a>
                  </div>

                  <div className="mb-10 grid auto-rows-[330px] gap-[30px] lg:grid-cols-[360px_360px_360px]">
                    {THREADS.map(({ key }) => {
                      const { title, updatedAt, desc } =
                        dictionary.helpCentre.feature2.threads[
                          key as ThreadKey
                        ];

                      return (
                        <div className="flex flex-col gap-4 rounded-[30px] bg-white px-5 py-[30px]">
                          <b className="text-[22px] leading-[30px]">{title}</b>
                          <p className="text-[#616669]">
                            {dictionary.helpCentre.feature2.updated}:{' '}
                            {updatedAt}
                          </p>
                          <p className="leading-[22px]">{desc}</p>
                          <div className="flex-1"></div>
                          <a
                            href="https://bettyes-organization.gitbook.io/scrm/learn-more/release-notes"
                            className="text-[#100B69]"
                          >
                            {dictionary.helpCentre.feature2.threads.readMore}
                          </a>
                        </div>
                      );
                    })}
                  </div>

                  <div className="flex flex-col gap-5 pb-[50px]">
                    <b className="text-2xl text-[#062436] lg:hidden">
                      {dictionary.helpCentre.feature2.gettingStarted}
                    </b>
                    <div className="flex flex-col text-2xl lg:flex-row lg:gap-2">
                      <span>{dictionary.helpCentre.feature2.scrmChampion}</span>
                      <a
                        href="https://docs.google.com/document/d/1UIb3WgRR63XN2ESi0JzIOvrdqfRA8uyMTQN6t9BBkcg/edit?tab=t.0"
                        className="text-[#1093FF]"
                      >
                        {dictionary.helpCentre.feature2.quickStartGuide}
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
