'use client';

import React, { useEffect, useRef, useState } from 'react';

const PageWrapper = ({ children }: { children: React.ReactNode }) => {
  const sectionsRef = useRef<HTMLDivElement[]>([]);
  const animationFrameId = useRef<number | null>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [activeSection, setActiveSection] = useState(0);

  // --- LOCKING MECHANISM ---
  const isAnimating = useRef(false);
  const lastWheelTime = useRef(0);
  // This cooldown will now apply to ALL input types.
  const INPUT_COOLDOWN = 800;

  // --- TALL COMPONENT HANDLING ---
  const [isScrollingWithinSection, setIsScrollingWithinSection] =
    useState(false);
  const [sectionScrollProgress, setSectionScrollProgress] = useState(0);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Helper function to check if a section is taller than viewport
  const isSectionTallerThanViewport = React.useCallback(
    (sectionIndex: number): boolean => {
      const section = sectionsRef.current[sectionIndex];
      if (!section) return false;
      return section.scrollHeight > window.innerHeight;
    },
    [],
  );

  // Helper function to get scroll progress within a section
  const getSectionScrollProgress = React.useCallback(
    (sectionIndex: number): number => {
      const section = sectionsRef.current[sectionIndex];
      if (!section) return 0;

      const sectionTop = section.offsetTop;
      const sectionHeight = section.scrollHeight;
      const viewportHeight = window.innerHeight;
      const currentScroll = window.pageYOffset;

      // Calculate how much of the section has been scrolled through
      const scrolledIntoSection = currentScroll - sectionTop;
      const maxScrollInSection = sectionHeight - viewportHeight;

      if (maxScrollInSection <= 0) return 1; // Section fits in viewport

      return Math.max(0, Math.min(1, scrolledIntoSection / maxScrollInSection));
    },
    [],
  );

  // Helper function to check if we're at the bottom of a tall section
  const isAtBottomOfSection = React.useCallback(
    (sectionIndex: number): boolean => {
      const progress = getSectionScrollProgress(sectionIndex);
      return progress >= 0.95; // Allow small tolerance
    },
    [getSectionScrollProgress],
  );

  useEffect(() => {
    // Centralized scroll logic, now treats all inputs the same.
    const performScroll = (direction: number) => {
      const currentTime = new Date().getTime();

      // --- UNIVERSAL LOCK CHECK ---
      // It now prevents spam from both wheel and keyboard.
      if (currentTime - lastWheelTime.current < INPUT_COOLDOWN) {
        return;
      }
      if (isAnimating.current) {
        return;
      }

      // Get current section index based on scroll position
      let currentSectionIndex = 0;
      for (let i = 0; i < sectionsRef.current.length; i++) {
        if (window.pageYOffset >= sectionsRef.current[i].offsetTop - 100) {
          currentSectionIndex = i;
        } else {
          break;
        }
      }

      // Check if current section is taller than viewport
      const isCurrentSectionTall =
        isSectionTallerThanViewport(currentSectionIndex);

      if (isCurrentSectionTall) {
        // If scrolling down and not at bottom of tall section, scroll within section
        if (direction > 0 && !isAtBottomOfSection(currentSectionIndex)) {
          setIsScrollingWithinSection(true);
          const progress = getSectionScrollProgress(currentSectionIndex);
          setSectionScrollProgress(progress);

          // Scroll within the section (smooth scroll by viewport height)
          const currentScroll = window.pageYOffset;
          const scrollAmount = window.innerHeight * 0.8; // Scroll 80% of viewport
          const targetPosition = currentScroll + scrollAmount;

          lastWheelTime.current = currentTime;
          window.scrollTo({ top: targetPosition, behavior: 'smooth' });
          return;
        }

        // If scrolling up and not at top of tall section, scroll within section
        if (direction < 0) {
          const section = sectionsRef.current[currentSectionIndex];
          const sectionTop = section.offsetTop;
          const currentScroll = window.pageYOffset;

          if (currentScroll > sectionTop + 50) {
            // Small tolerance
            setIsScrollingWithinSection(true);
            const scrollAmount = window.innerHeight * 0.8;
            const targetPosition = Math.max(
              sectionTop,
              currentScroll - scrollAmount,
            );

            lastWheelTime.current = currentTime;
            window.scrollTo({ top: targetPosition, behavior: 'smooth' });
            return;
          }
        }
      }

      // Normal section-to-section scrolling
      setIsScrollingWithinSection(false);
      const nextSectionIndex = currentSectionIndex + direction;

      // Handle scrolling to top/bottom edges
      if (
        nextSectionIndex < 0 ||
        nextSectionIndex >= sectionsRef.current.length
      ) {
        lastWheelTime.current = currentTime;
        const targetPosition =
          nextSectionIndex < 0 ? 0 : document.documentElement.scrollHeight;
        window.scrollTo({ top: targetPosition, behavior: 'smooth' });

        if (nextSectionIndex < 0) setActiveSection(0);
        else setActiveSection(sectionsRef.current.length - 1);
        return;
      }

      // --- START THE SCROLL ---
      isAnimating.current = true;
      lastWheelTime.current = currentTime;

      const targetSection = sectionsRef.current[nextSectionIndex];
      const targetPosition = targetSection.offsetTop;
      const startPosition = window.pageYOffset;
      const distance = targetPosition - startPosition;
      const duration = 200;
      let startTime: number | null = null;

      const animateScroll = (time: number) => {
        if (startTime === null) startTime = time;
        const timeElapsed = time - startTime;
        const run = ease(timeElapsed, startPosition, distance, duration);
        window.scrollTo(0, run);

        if (timeElapsed < duration) {
          animationFrameId.current = requestAnimationFrame(animateScroll);
        } else {
          window.scrollTo(0, targetPosition);
          animationFrameId.current = null;
          setActiveSection(nextSectionIndex);
          isAnimating.current = false;
        }
      };

      animationFrameId.current = requestAnimationFrame(animateScroll);
    };

    // Handler for Mouse Wheel and Touchpad
    const handleWheel = (event: WheelEvent) => {
      event.preventDefault();
      if (Math.abs(event.deltaY) < 10) {
        return;
      }
      const direction = event.deltaY > 0 ? 1 : -1;
      performScroll(direction);
    };

    // Handler for Keyboard Navigation
    const handleKeyDown = (event: KeyboardEvent) => {
      const navKeys = [
        'ArrowUp',
        'ArrowDown',
        'PageUp',
        'PageDown',
        'Space',
        'Home',
        'End',
      ];
      if (navKeys.includes(event.code)) {
        event.preventDefault();
      }

      switch (event.code) {
        case 'ArrowDown':
        case 'PageDown':
        case 'Space':
          performScroll(1);
          break;
        case 'ArrowUp':
        case 'PageUp':
          performScroll(-1);
          break;
        case 'Home':
          // Scroll to top
          window.scrollTo({ top: 0, behavior: 'smooth' });
          setActiveSection(0);
          break;
        case 'End':
          // Scroll to bottom
          window.scrollTo({
            top: document.documentElement.scrollHeight,
            behavior: 'smooth',
          });
          setActiveSection(sectionsRef.current.length - 1);
          break;
      }
    };

    const ease = (t: number, b: number, c: number, d: number) => {
      t /= d / 2;
      if (t < 1) return (c / 2) * t * t * t + b;
      t -= 2;
      return (c / 2) * (t * t * t + 2) + b;
    };

    if (!isMobile) {
      window.addEventListener('wheel', handleWheel, { passive: false });
      window.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      window.removeEventListener('wheel', handleWheel);
      window.removeEventListener('keydown', handleKeyDown);
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current);
      }
    };
  }, [
    isMobile,
    activeSection,
    isSectionTallerThanViewport,
    isAtBottomOfSection,
    getSectionScrollProgress,
  ]);

  return (
    <div>
      {React.Children.map(children, (child, index) => (
        <div
          ref={(el) => {
            if (el) sectionsRef.current[index] = el;
          }}
        >
          {React.cloneElement(child as React.ReactElement, {
            isActive: index === activeSection,
          })}
        </div>
      ))}
    </div>
  );
};

export default PageWrapper;
