'use client';

import React, { useEffect, useRef, useState } from 'react';

const PageWrapper = ({ children }: { children: React.ReactNode }) => {
  const sectionsRef = useRef<HTMLDivElement[]>([]);
  const animationFrameId = useRef<number | null>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [activeSection, setActiveSection] = useState(0);

  // --- LOCKING MECHANISM ---
  const isAnimating = useRef(false);
  const lastWheelTime = useRef(0);
  // This cooldown will now apply to ALL input types.
  const INPUT_COOLDOWN = 800;

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  useEffect(() => {
    // Centralized scroll logic, now treats all inputs the same.
    // CHANGED: Removed the 'isKeyboard' parameter.
    const performScroll = (nextSectionIndex: number) => {
      const currentTime = new Date().getTime();

      // --- UNIVERSAL LOCK CHECK ---
      // CHANGED: The cooldown check no longer cares about the input source.
      // It now prevents spam from both wheel and keyboard.
      if (currentTime - lastWheelTime.current < INPUT_COOLDOWN) {
        return;
      }
      if (isAnimating.current) {
        return;
      }

      // Handle scrolling to top/bottom edges
      if (
        nextSectionIndex < 0 ||
        nextSectionIndex >= sectionsRef.current.length
      ) {
        lastWheelTime.current = currentTime;
        const targetPosition =
          nextSectionIndex < 0 ? 0 : document.documentElement.scrollHeight;
        window.scrollTo({ top: targetPosition, behavior: 'smooth' });

        if (nextSectionIndex < 0) setActiveSection(0);
        else setActiveSection(sectionsRef.current.length - 1);
        return;
      }

      // --- START THE SCROLL ---
      isAnimating.current = true;
      lastWheelTime.current = currentTime;

      const targetSection = sectionsRef.current[nextSectionIndex];
      const targetPosition = targetSection.offsetTop;
      const startPosition = window.pageYOffset;
      const distance = targetPosition - startPosition;
      const duration = 200;
      let startTime: number | null = null;

      const animateScroll = (time: number) => {
        if (startTime === null) startTime = time;
        const timeElapsed = time - startTime;
        const run = ease(timeElapsed, startPosition, distance, duration);
        window.scrollTo(0, run);

        if (timeElapsed < duration) {
          animationFrameId.current = requestAnimationFrame(animateScroll);
        } else {
          window.scrollTo(0, targetPosition);
          animationFrameId.current = null;
          setActiveSection(nextSectionIndex);
          isAnimating.current = false;
        }
      };

      animationFrameId.current = requestAnimationFrame(animateScroll);
    };

    // Handler for Mouse Wheel and Touchpad (unchanged)
    const handleWheel = (event: WheelEvent) => {
      event.preventDefault();
      if (Math.abs(event.deltaY) < 10) {
        return;
      }
      const direction = event.deltaY > 0 ? 1 : -1;
      let currentSectionIndex = 0;
      for (let i = 0; i < sectionsRef.current.length; i++) {
        if (window.pageYOffset >= sectionsRef.current[i].offsetTop - 100) {
          currentSectionIndex = i;
        } else {
          break;
        }
      }
      const nextSectionIndex = currentSectionIndex + direction;
      performScroll(nextSectionIndex);
    };

    // Handler for Keyboard Navigation
    const handleKeyDown = (event: KeyboardEvent) => {
      const navKeys = [
        'ArrowUp',
        'ArrowDown',
        'PageUp',
        'PageDown',
        'Space',
        'Home',
        'End',
      ];
      if (navKeys.includes(event.code)) {
        event.preventDefault();
      }

      let nextSectionIndex = activeSection;

      switch (event.code) {
        case 'ArrowDown':
        case 'PageDown':
        case 'Space':
          nextSectionIndex = activeSection + 1;
          // CHANGED: Call performScroll without the 'true' flag.
          performScroll(nextSectionIndex);
          break;
        case 'ArrowUp':
        case 'PageUp':
          nextSectionIndex = activeSection - 1;
          // CHANGED: Call performScroll without the 'true' flag.
          performScroll(nextSectionIndex);
          break;
        case 'Home':
          // CHANGED: Call performScroll without the 'true' flag.
          performScroll(-1);
          break;
        case 'End':
          // CHANGED: Call performScroll without the 'true' flag.
          performScroll(sectionsRef.current.length);
          break;
      }
    };

    const ease = (t: number, b: number, c: number, d: number) => {
      t /= d / 2;
      if (t < 1) return (c / 2) * t * t * t + b;
      t -= 2;
      return (c / 2) * (t * t * t + 2) + b;
    };

    if (!isMobile) {
      window.addEventListener('wheel', handleWheel, { passive: false });
      window.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      window.removeEventListener('wheel', handleWheel);
      window.removeEventListener('keydown', handleKeyDown);
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current);
      }
    };
  }, [isMobile, activeSection]);

  return (
    <div>
      {React.Children.map(children, (child, index) => (
        <div
          ref={(el) => {
            if (el) sectionsRef.current[index] = el;
          }}
        >
          {React.cloneElement(child as React.ReactElement, {
            isActive: index === activeSection,
          })}
        </div>
      ))}
    </div>
  );
};

export default PageWrapper;
