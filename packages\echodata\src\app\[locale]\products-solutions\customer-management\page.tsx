import ProductBgBottomLeft from '@hi7/assets/background/product-4-bg-bottom-left.svg';
import ProductBgBottomRight from '@hi7/assets/background/product-4-bg-bottom-right.svg';
import ProductBgTopLeft from '@hi7/assets/background/product-4-bg-top-left.svg';
import ProductBgTopRight from '@hi7/assets/background/product-4-bg-top-right.svg';
import Product1 from '@hi7/assets/icon/product-4-1.svg';
import Product2 from '@hi7/assets/icon/product-4-2.svg';
import Product3 from '@hi7/assets/icon/product-4-3.svg';

import TriggerAnimation from '@hi7/components/TriggerAnimation';
import type { Dictionary } from '@hi7/interface/dictionary';
import type { LocaleProps } from '@hi7/interface/i18n';
import { getDictionary } from '@hi7/lib/i18n';
import GetStarted from './GetStarted';

async function page({ params }: LocaleProps) {
  const { locale } = params;
  const dictionary: Dictionary = await getDictionary(locale);
  return (
    <>
      <div className="flex items-center justify-center overflow-hidden bg-linear-[180deg,#172DB1_0%,#90C6FF_50%,#E1EFFF_100%]">
        <div className="w-full">
          <div className="relative lg:min-h-[65dvh]">
            <div className="lg:animate-product-4-top-ring-left absolute top-[50px] left-0 w-[55px] lg:top-0 lg:h-[65dvh] lg:w-auto">
              <ProductBgTopLeft height="100%" />
            </div>
            <div className="lg:animate-product-4-top-ring-right absolute top-[50px] right-0 w-[55px] lg:top-0 lg:h-[65dvh] lg:w-auto">
              <ProductBgTopRight height="100%" />
            </div>

            <div className="relative mb-14 flex flex-col items-center justify-center pt-16 text-white lg:pt-[320px]">
              <div className="lg:animate-product-4-top-title flex max-w-[320px] flex-col text-center lg:max-w-dvw lg:opacity-0">
                <h1 className="mb-5 text-[40px] leading-[48px] font-bold lg:text-[50px] lg:leading-[55px]">
                  {dictionary.productSolution.customerManagement.landing.title}
                </h1>

                <h3 className="text-[20px] leading-7">
                  {dictionary.productSolution.customerManagement.landing.desc}
                </h3>
              </div>
            </div>
          </div>

          <TriggerAnimation>
            <div className="relative flex flex-col items-center px-5 lg:px-[250px] lg:pb-[160px]">
              <div className="lg:animate-product-4-bottom-ring-left absolute top-[-25%] left-[-150px] w-[200px] lg:top-[20%] lg:left-0 lg:w-[200px] lg:translate-[-50%_-50%] lg:opacity-0">
                <ProductBgBottomLeft width="100%" />
              </div>
              <div className="lg:animate-product-4-bottom-ring-right absolute top-[90%] right-[-20px] w-[150px] lg:top-[28%] lg:right-0 lg:translate-[-65%_-50%] lg:opacity-0">
                <ProductBgBottomRight width="100%" />
              </div>
              <h1 className="lg:animate-product-4-bottom-title mb-16 text-center text-[32px] leading-[38px] font-bold lg:text-[50px] lg:leading-[55px] lg:opacity-0">
                {dictionary.productSolution.customerManagement.feature.title}
              </h1>
              <div className="lg:animate-product-4-bottom-content relative z-10 grid w-full grid-cols-1 justify-start gap-x-[120px] gap-y-8 pb-10 lg:grid-cols-3 lg:opacity-0">
                <div className="flex flex-col gap-3">
                  <Product1 />
                  <b className="text-[22px] leading-[26px]">
                    {
                      dictionary.productSolution.customerManagement.feature
                        .feature1.title
                    }
                  </b>
                  <p className="text-[16px]">
                    {
                      dictionary.productSolution.customerManagement.feature
                        .feature1.desc
                    }
                  </p>
                </div>
                <div className="flex flex-col gap-3">
                  <Product2 />
                  <b>
                    {
                      dictionary.productSolution.customerManagement.feature
                        .feature2.title
                    }
                  </b>
                  <p className="text-[16px]">
                    {
                      dictionary.productSolution.customerManagement.feature
                        .feature2.desc
                    }
                  </p>
                </div>

                <div className="flex flex-col gap-3">
                  <Product3 />
                  <b className="text-[22px] leading-[26px]">
                    {
                      dictionary.productSolution.customerManagement.feature
                        .feature3.title
                    }
                  </b>
                  <p className="text-[16px]">
                    {
                      dictionary.productSolution.customerManagement.feature
                        .feature3.desc
                    }
                  </p>
                </div>
              </div>
            </div>
          </TriggerAnimation>
        </div>
      </div>
      <GetStarted dictionary={dictionary} locale={locale} />
    </>
  );
}

export default page;
