'use client';

import type { DictionaryProps } from '@hi7/interface/i18n';
import Responsive from '../Responsive';
import HeaderDesktop from './Desktop';
import Mobile from './Mobile';

// type HeaderProps = Pick<i18n, 'dictionary'>;
// { dictionary }: HeaderProps
const Header = ({ dictionary }: DictionaryProps) => {
  return (
    <Responsive
      desktop={<HeaderDesktop dictionary={dictionary} />}
      mobile={<Mobile dictionary={dictionary} />}
    />
  );
};

export default Header;
