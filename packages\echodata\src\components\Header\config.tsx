// import type { MenuLinkProps } from '@hi7/interface/link';
// import P1 from './icons/p1.svg';
// import P2 from './icons/p2.svg';
// import P3 from './icons/p3.svg';
// import P4 from './icons/p4.svg';
// import P5 from './icons/p5.svg';
// import P6 from './icons/p6.svg';

// export const ROUTES: MenuLinkProps[] = [
//   {
//     url: '/products-solutions',
//     children: 'Product Solution',
//     items: [
//       {
//         icon: <P1 />,
//         url: '/products-solutions/whatsapp-marketing-management',
//         text: 'WhatsApp Marketing Management',
//       },
//       {
//         icon: <P2 />,
//         url: '/products-solutions/social-media-platform',
//         text: 'Social Media Platforms',
//       },
//       {
//         icon: <P3 />,
//         url: '/products-solutions/customer-service-saas-tool',
//         text: 'Customer Service SaaS Tool',
//       },
//       {
//         icon: <P4 />,
//         url: '/products-solutions/customer-management',
//         text: 'Customer Management',
//       },
//     ],
//   },
//   {
//     url: '/pricing',
//     children: 'Pricing',
//   },
//   {
//     url: '/industry-insights',
//     children: 'Industry Insights',
//   },
//   {
//     url: '/contact-us',
//     children: 'Contact Us',
//     items: [
//       {
//         icon: <P5 />,
//         url: '/contact-us',
//         text: 'Contact Us',
//       },
//       {
//         icon: <P6 />,
//         url: '/help-center',
//         text: 'Help Center',
//       },
//     ],
//   },
//   {
//     url: '/download',
//     children: 'Downloads',
//   },
//   {
//     asButton: true,
//     url: '/signup',
//     children: 'Sign Up/Log in',
//   },
// ];
