import type React from 'react';
import type {
  FieldV<PERSON>ues,
  SubmitHandler,
  UseFormReturn,
} from 'react-hook-form';
import { FormProvider, useForm as useLibForm } from 'react-hook-form';

type FormProps<T extends FieldValues> = {
  children: React.ReactNode;
  onSubmit: SubmitHandler<T>;
  onReset?: () => void;
  form: UseFormReturn<T>;
  name: string;
};

const Form = <T extends FieldValues>({
  children,
  onSubmit,
  onReset,
  form,
  name,
}: FormProps<T>) => (
  <FormProvider {...form}>
    <form
      name={name}
      onSubmit={(e) => {
        e.preventDefault();
        form.handleSubmit(onSubmit)();
      }}
      onReset={(e) => {
        e.preventDefault();
        form.reset();
        onReset?.();
      }}
    >
      {children}
    </form>
  </FormProvider>
);

export const useFormHelper = useLibForm;
export default Form;
