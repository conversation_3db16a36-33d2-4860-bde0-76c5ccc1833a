'use client';

import Star from '@hi7/assets/icon/star.svg';
import type { DictionaryProps } from '@hi7/interface/i18n';
import clsx from 'clsx';
import Image from 'next/image';
import { useEffect, useRef, useState } from 'react';
import TriggerAnimation from '../TriggerAnimation';
import type { ClientKey } from './config';
import { FEEDBACKS } from './config';

function Feedback({ dictionary }: DictionaryProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const innerScrollRef = useRef<HTMLDivElement>(null);

  const [scrollPosition, setScrollPosition] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      if (containerRef.current) {
        const scrollLeft = containerRef.current.scrollLeft;
        setScrollPosition(scrollLeft);
      }
    };

    const container = containerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
    }

    return () => {
      if (container) {
        container.removeEventListener('scroll', handleScroll);
      }
    };
  }, []);

  useEffect(() => {
    const indicator = innerScrollRef.current;
    if (indicator && containerRef.current) {
      const maxScroll =
        containerRef.current.scrollWidth - containerRef.current.clientWidth;
      const scrollPercentage = scrollPosition / maxScroll;
      indicator.style.transform = `translateX(${scrollPercentage * 64}px)`;
    }
  }, [scrollPosition]);

  return (
    <TriggerAnimation>
      <div className="flex items-center justify-center overflow-hidden bg-linear-[180deg,#E4F2FF_0%,#C9D1F1_100%] text-black">
        <div className="w-full">
          <div className="relative min-h-[820px]">
            <div className="flex flex-col items-center justify-center px-8 pt-[70px] pb-[60px] lg:pt-[140px]">
              <h2 className="lg:animate-home-feedback-title text-[32px] leading-[38.4px] font-bold lg:translate-y-[-100%] lg:text-[50px] lg:leading-[58px] lg:opacity-0">
                {dictionary.home.client.title}
              </h2>
            </div>
            <div className="relative h-[480px]">
              <div
                className="no-scrollbar absolute left-0 w-full overflow-x-auto"
                ref={containerRef}
              >
                <div className="grid w-[1100px] grid-cols-[310px_310px_310px] gap-x-[40px] px-10 pt-[70px] lg:w-auto lg:items-center lg:justify-center lg:gap-x-[95px]">
                  {FEEDBACKS.map(({ image, key }, index) => {
                    const { name, role, desc } =
                      dictionary.home.client[key as ClientKey];

                    return (
                      <div
                        key={index}
                        className={clsx('relative h-[400px] w-[310px]')}
                      >
                        <div
                          className={clsx(
                            'absolute top-0 right-0 bottom-0 left-0 rounded-[28px] bg-[#F5CC00]',
                            // yes, it's stupid hack for animation to render in dynamic way
                            // TODO: fix it by https://tailwindcss.com/docs/detecting-classes-in-source-files
                            index === 0 &&
                              `lg:animate-home-feedback-bg-0-block lg:translate-x-[-100%]`,
                            index === 1 &&
                              `lg:animate-home-feedback-bg-1-block lg:translate-y-[-120%]`,
                            index === 2 &&
                              `lg:animate-home-feedback-bg-2-block lg:translate-x-[100%]`,
                          )}
                        ></div>

                        <div className="lg:animate-home-feedback-block relative flex flex-col items-center rounded-[28px] px-9 py-10 lg:opacity-0">
                          <div className="absolute top-[-70px] left-[50%] flex h-[92px] w-[92px] translate-x-[-50%] items-center justify-center overflow-hidden rounded-full bg-gray-300">
                            <Image width={92} height={92} src={image} alt="" />
                          </div>
                          <b>{name}</b>
                          <p className="text-[14px]">{role}</p>
                          <div className="my-2.5 flex items-center justify-center gap-1">
                            <Star />
                            <Star />
                            <Star />
                            <Star />
                            <Star />
                          </div>
                          <p className="text-center text-[14px]">{desc}</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>

            <div className="relative m-auto mt-8 h-[9px] w-[100px] rounded-2xl bg-[#D9D9D9]/80 lg:hidden">
              <div
                className="absolute left-0 h-[9px] w-[36px] rounded-2xl bg-[#1093FF] transition-all duration-300 ease-in-out"
                ref={innerScrollRef}
              ></div>
            </div>
          </div>
        </div>
      </div>
    </TriggerAnimation>
  );
}

export default Feedback;
