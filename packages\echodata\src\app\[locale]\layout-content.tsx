'use client';

import Footer from '@hi7/components/Footer';
import Header from '@hi7/components/Header';
import type { i18n } from '@hi7/interface/i18n';
import { type ReactNode } from 'react';

type LayoutContainerProps = {
  children: ReactNode;
  pathname: string;
} & i18n;

const LayoutContent = ({
  children,
  pathname,
  dictionary,
  locale,
}: LayoutContainerProps) => {
  return (
    <main id="main">
      <Header dictionary={dictionary} />
      {children}
      <Footer dictionary={dictionary} currentLocale={locale} />
    </main>
  );
};

export default LayoutContent;
