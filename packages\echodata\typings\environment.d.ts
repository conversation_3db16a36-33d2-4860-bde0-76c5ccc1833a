declare global {
  namespace NodeJS {
    interface ProcessEnv {
      VERSION: string;
      ENVIRONMENT: 'development' | 'staging' | 'production';
      SERVICE_NAME: 'Hiseven-Web';

      NEXT_PUBLIC_API_URL: string;
      NEXT_PUBLIC_S3_URL: string;
      NEXT_PUBLIC_HI7_GA_TAG: string;
      NEXT_PUBLIC_HI7_GA_WA_CONVERSION_LABEL: string;
      NEXT_PUBLIC_HI7_GA_LEAD_FORM_CONVERSION_LABEL: string;

      WP_API_URL: string;
      FF_API_URL: string;
      FF_WEB_URL: string;
      HI7_FB_TAG: string;
      HI7_GA_TAG: string;
      HI7_GA_PAGE_VIEW_CONVERSION_LABEL: string;
      HI7_GA_ALT_TAG: string;
    }

    interface Window {
      gtag: (...args: unknown[]) => void;
      gtag_report_conversion: (url?: string) => void;
    }
  }
}

export {};
