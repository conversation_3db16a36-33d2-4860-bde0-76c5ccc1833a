import Feature1 from '@hi7/assets/icon/feature1.svg';
import Feature2 from '@hi7/assets/icon/feature2.svg';
import Feature3 from '@hi7/assets/icon/feature3.svg';
import type { DictionaryProps } from '@hi7/interface/i18n';
import { Arsenal } from 'next/font/google';

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
  display: 'auto',
});

function Feature({ dictionary }: DictionaryProps) {

  const features = [
    {
      icon: Feature1,
      title: dictionary.features.globalDataAcquisition.feature2.globalDataAcquisition.title,
      desc: dictionary.features.globalDataAcquisition.feature2.globalDataAcquisition.desc,
    },
    {
      icon: Feature2,
      title: dictionary.features.globalDataAcquisition.feature2.customNumberSegmentation.title,
      desc: dictionary.features.globalDataAcquisition.feature2.customNumberSegmentation.desc,
    },
    {
      icon: Feature3,
      title: dictionary.features.globalDataAcquisition.feature2.globalNumberGeneration.title,
      desc: dictionary.features.globalDataAcquisition.feature2.globalNumberGeneration.desc,
    }
  ];

  return (
    <div className="relative z-10 flex items-center justify-center overflow-hidden bg-[#047AFF] mt-15 rounded-[30px] px-8 lg:rounded-[80px] lg:mx-16 lg:-mb-5 xl:mx-30">
      <div className="w-full h-full">
        <div className="relative">
          <div className="flex flex-col items-start justify-center pt-12 text-white pb-15 lg:pt-20 lg:items-start lg:px-8 lg:pb-12 xl:pt-26 xl:pb-12">
            <h2 className={`lg:animate-home-feature-1-title mb-8 text-[40px] leading-[40px] font-bold lg:translate-y-[-100%] lg:text-[64px] lg:leading-[58px] lg:opacity-0 lg:mb-3 ${arsenal.className}`}>
              {dictionary.features.globalDataAcquisition.feature2.title}
            </h2>
            <p className='w-[70%] text-[16px] font-thin lg:text-[24px] lg:font-[300] xl:w-[40%]'>{dictionary.features.globalDataAcquisition.feature2.desc}</p>
            <hr className='mt-5 w-full lg:my-7' />
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 lg:gap-10 mt-6 lg:mt-5 xl:gap-13">
              {
                features.length > 0 && (
                  features.map((feature, index) => (
                    <div className="flex flex-col gap-4 rounded-[30px] bg-[#E9F3FF] p-7 w-full text-[#047AFF] mb-0 lg:p-6 lg:rounded-[20px] xl:p-10">
                      <div className='flex flex-row items-center justify-center xl:justify-start'>
                        <feature.icon className='w-[30%] xl:w-[15%] xl:scale-125' />
                        <h1 className='text-[20px] font-[500] mt-1 lg:text-[24px] leading-[25px] xl:text-[25px]'>{feature.title}</h1>
                      </div>
                      <hr />
                      <span className='text-[16px] font-[300] xl:text-[22px] xl:font-[400]'>
                        {feature.desc}
                      </span>
                    </div>
                  ))
                )
              }
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Feature;
