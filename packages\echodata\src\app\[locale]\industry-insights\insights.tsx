'use client';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import type { Dictionary } from '@hi7/interface/dictionary';
import type { Locale } from '@hi7/lib/i18n';

import GetStarted from './GetStarted';
import Hero from './hero';
import ImageGrid from './imageGrid';
import Navbar from './navBar';
import SearchBar from './searchBar';

interface IndustryInsightsProps {
  dictionary: Dictionary;
  locale: Locale;
  categories: string[];
}

const IndustryInsights = ({
  dictionary,
  locale,
  categories,
}: IndustryInsightsProps) => {
  const searchParams = useSearchParams();
  const initialCategory = searchParams.get('category') || categories[0];

  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);
  const [search, setSearch] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(initialCategory);

  // Convert navbar category name to ARTICLES object key
  const getCategoryKey = (categoryName: string) => {
    if (categoryName === categories[0]) return 'all';

    // Map navbar categories to ARTICLES object keys
    const categoryMap: { [key: string]: string } = {
      [categories[1]]: 'CustomerAcquisition',
      [categories[2]]: 'AccountDataFiltering',
      [categories[3]]: 'MassMarketing',
      [categories[4]]: 'SocialMediaMarketing',
    };

    if (locale === 'zh') {
      categoryMap[categories[5]] = 'MarketingResearch';
      categoryMap[categories[6]] = 'SelectedTopics';
      categoryMap[categories[7]] = 'ServiceSupport';
    }

    return categoryMap[categoryName] || 'CustomerAcquisition'; // fallback
  };

  // Handle category change from navbar
  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    setPage(1); // Reset to first page when category changes
  };

  // Reset to first page when search or page size changes
  useEffect(() => {
    setPage(1);
  }, [search, pageSize]);

  return (
    <div className="relative bg-[#e9f3ff]">
      <Hero dictionary={dictionary} locale={locale} />
      <Navbar
        onCategoryChange={handleCategoryChange}
        activeCategory={selectedCategory}
        categories={categories}
      />
      <SearchBar search={search} setSearch={setSearch} />
      <ImageGrid
        dictionary={dictionary}
        category={getCategoryKey(selectedCategory)}
        search={search}
        page={page}
        pageSize={pageSize}
        onPageChange={setPage}
        onPageSizeChange={setPageSize}
        locale={locale}
      />
      <GetStarted dictionary={dictionary} />
    </div>
  );
};

export default IndustryInsights;
