import Feature1 from '@hi7/assets/icon/feature1.svg';
import Feature2 from '@hi7/assets/icon/feature2.svg';
import Feature3 from '@hi7/assets/icon/feature3.svg';
import Feature4 from '@hi7/assets/icon/feature4.svg';
import Feature5 from '@hi7/assets/icon/feature5.svg';
import type { DictionaryProps } from '@hi7/interface/i18n';
import { Arsenal } from 'next/font/google';
import TriggerAnimation from '../TriggerAnimation';

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
  display: 'auto',
});

function Feature({ dictionary }: DictionaryProps) {

  const features = [
    {
      icon: Feature1,
      title: dictionary.home.feature1.globalNumberGeneration.title,
      desc: dictionary.home.feature1.globalNumberGeneration.desc,
      points: dictionary.home.feature1.globalNumberGeneration.points,
    },
    {
      icon: Feature2,
      title: dictionary.home.feature1.whatsappFiltering.title,
      desc: dictionary.home.feature1.whatsappFiltering.desc,
      points: dictionary.home.feature1.whatsappFiltering.points,
    },
    {
      icon: Feature3,
      title: dictionary.home.feature1.telegramFiltering.title,
      desc: dictionary.home.feature1.telegramFiltering.desc,
      points: dictionary.home.feature1.telegramFiltering.points,
    },
    {
      icon: Feature4,
      title: dictionary.home.feature1.oneStopMarketing.title,
      desc: dictionary.home.feature1.oneStopMarketing.desc,
      points: dictionary.home.feature1.oneStopMarketing.points,
    },
    {
      icon: Feature5,
      title: dictionary.home.feature1.taskFlowFiltering.title,
      desc: dictionary.home.feature1.taskFlowFiltering.desc,
      points: dictionary.home.feature1.taskFlowFiltering.points,
    },
  ];

  return (
    <TriggerAnimation>
      <div className="flex items-center justify-center overflow-hidden bg-[#047AFF] mt-15 rounded-[30px] rounded-bl-none px-8 lg:rounded-[150px] lg:rounded-bl-none lg:rounded-br-[300px] lg:pb-8 xl:px-15 xl:mt-40">
        <div className="w-full h-full">
          <div className="relative lg:min-h-[920px]">

            <div className="flex flex-col items-start justify-center pt-12 text-white pb-15 lg:pt-20 lg:items-start lg:pl-7 lg:pb-0 xl:pt-26 xl:pb-12">
              <h2 className={`lg:animate-home-feature-1-title mb-8 text-[40px] leading-[40px] font-bold lg:translate-y-[-100%] lg:text-[64px] lg:leading-[58px] lg:opacity-0 ${arsenal.className}`}>
                {dictionary.home.feature1.title}
              </h2>
              <p className='text-[16px] font-thin lg:text-[24px]'>{dictionary.home.feature1.desc}</p>
              <hr className='mt-5 w-full md:hidden' />
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 mt-6 lg:mt-5 xl:gap-13 xl:mt-15">
                {
                  features.length > 0 && (
                    features.map((feature, index) => (
                      <div className="flex flex-col gap-4 rounded-[30px] bg-[#E9F3FF] p-7 w-full text-[#047AFF] mb-0 lg:p-10">
                        <div className='flex flex-row items-center justify-center xl:justify-start'>
                          <feature.icon className='w-[30%] xl:w-[15%]' />
                          <h1 className='text-[20px] font-[500] mt-1 lg:text-[24px] leading-[25px]'>{feature.title}</h1>
                        </div>
                        <hr />
                        <span className='text-[16px] font-[300]'>
                          {feature.desc}
                        </span>
                        <ul className="text-[16px] font-[300] -mt-4">
                          {feature.points
                            .split('\n')
                            .map((point, index) => (
                              <li key={index}>{point}</li>
                            ))}
                        </ul>
                      </div>
                    ))
                  )
                }
              </div>
            </div>
          </div>
        </div>
      </div>
    </TriggerAnimation>
  );
}

export default Feature;
