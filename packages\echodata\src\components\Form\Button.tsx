import clsx from 'clsx';
import type React from 'react';
import { type ReactNode } from 'react';
type HelperProps = {
  children: string | ReactNode;
  disabled?: boolean;
  className?: string;
};

const Button: React.FC<HelperProps> = ({
  children,
  disabled,
  className = 'rounded-md',
}) => {
  return (
    <button
      type="submit"
      className={clsx(
        'px-5 py-3 focus-visible:outline-hidden',
        disabled
          ? 'bg-[#F5F5F5] text-[#1F1F1F]'
          : 'bg-hi7-primary text-hi7-light hover:bg-hi7-golden',
        className,
      )}
      disabled={disabled}
    >
      {children}
    </button>
  );
};

export default Button;
