:root {
  --download-animation-duration: 1s;
  --download-animation-half-duration: 0.5s;
}

@theme {
  --animate-download-bg-left-1: downloadBgLeft1
    var(--product-animation-duration) ease-in-out forwards;
  @keyframes downloadBgLeft1 {
    0% {
      transform: translateX(0);
    }

    100% {
      transform: translateX(100%);
    }
  }

  --animate-download-bg-left-2: downloadBgLeft2
    var(--product-animation-duration) var(--product-animation-half-duration)
    ease-in-out forwards;
  @keyframes downloadBgLeft2 {
    0% {
      opacity: 0;
      transform: translateX(0);
    }

    100% {
      opacity: 1;
      transform: translateX(100%);
    }
  }

  --animate-download-picture: downloadPicture var(--product-animation-duration)
    ease-in-out forwards;
  @keyframes downloadPicture {
    0% {
      transform: translateX(100%);
    }

    100% {
      transform: translateX(-10%);
    }
  }

  --animate-download-title: downloadTitle var(--product-animation-duration)
    var(--product-animation-half-duration) ease-in-out forwards;
  @keyframes downloadTitle {
    0% {
      opacity: 0;
      transform: translateX(0);
    }

    100% {
      opacity: 1;
      transform: translateX(120%);
    }
  }

  --animate-download-bg-right: downloadBgRight var(--product-animation-duration)
    var(--product-animation-half-duration) ease-in-out forwards;
  @keyframes downloadBgRight {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }

  --animate-download-slider: downloadSlider var(--product-animation-duration)
    var(--product-animation-duration) ease-in-out forwards;
  @keyframes downloadSlider {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }
}
