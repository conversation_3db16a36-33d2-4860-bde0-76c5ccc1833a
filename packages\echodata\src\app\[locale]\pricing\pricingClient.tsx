'use client';

import Check from '@hi7/assets/icon/check.svg';
import Dash from '@hi7/assets/icon/dash.svg';
import Minus from '@hi7/assets/icon/minus.svg';
import Plus from '@hi7/assets/icon/plus.svg';
import useScreenSize from '@hi7/helpers/useScreenSize';
import clsx from 'clsx';
import { useEffect, useRef, useState } from 'react';
import { PRICES } from './config';

import TriggerAnimation from '@hi7/components/TriggerAnimation';
import type { DictionaryProps } from '@hi7/interface/i18n';
import { Locale } from '@hi7/lib/i18n';
import GetStarted from './GetStarted';
import { removeIcon, withIcon } from './helper';

interface PricingClientProps extends DictionaryProps {
  locale: Locale;
}

export default function PricingClient({
  dictionary,
  locale,
}: PricingClientProps) {
  const [open, setOpen] = useState<Record<string, boolean>>({});
  const [selected, setSelected] = useState<string>(Object.keys(PRICES)[0]);
  const { isMobile } = useScreenSize();
  const [isSticky, setIsSticky] = useState(false);
  const actionBarRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      if (!actionBarRef.current) return;
      const rect = actionBarRef.current.getBoundingClientRect();
      // If the bottom of the bar is above the viewport, make it sticky
      const actionBarHeight = rect.height;
      setIsSticky(
        rect.bottom < 0 || rect.top > window.innerHeight - actionBarHeight,
      );
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    window.addEventListener('resize', handleScroll);
    handleScroll();

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleScroll);
    };
  }, []);

  return (
    <>
      <div className="flex items-center justify-center overflow-hidden bg-[#0506DC] lg:bg-linear-[180deg,#0506DC_60%,#6162E1_75%,#FFF_100%]">
        <div className="w-full max-w-[1440px]">
          <div className="relative lg:min-h-[810px]">
            <div className="lg:animate-price-top-content items-center justify-center gap-[72px] pt-14 text-white lg:flex lg:pt-[150px] lg:pb-[46px] lg:opacity-0">
              <div className="m-auto flex max-w-[300px] flex-col text-center lg:text-left">
                <h1
                  className="mb-5 text-[40px] leading-[48px] font-bold lg:text-[50px] lg:leading-[55px]"
                  dangerouslySetInnerHTML={{
                    __html: dictionary.pricing.landing.title,
                  }}
                />
                <h3 className="text-[20px] leading-7">
                  {dictionary.pricing.landing.desc}
                </h3>
              </div>
              <div className="mt-9 flex gap-3 px-6 lg:hidden">
                {Object.keys(PRICES).map((pack) => (
                  <button
                    key={pack}
                    onClick={() => {
                      setSelected(pack);
                    }}
                    className={clsx(
                      'w-full cursor-pointer rounded-[50px] px-6 py-3 whitespace-nowrap capitalize',
                      selected === pack
                        ? 'cursor-pointer bg-[#F5CC00] text-[#1E1E1E]'
                        : 'border-2 border-[#F5CC00] text-[#F5CC00]',
                    )}
                  >
                    {dictionary.pricing.table.header[
                      pack as keyof typeof dictionary.pricing.table.header
                    ] ?? pack}
                  </button>
                ))}
              </div>
              <div className="grid auto-rows-[520px] gap-3.5 px-6 pt-[78px] lg:auto-rows-[340px] lg:grid-cols-[270px_270px_270px] lg:px-0 lg:pt-[45px]">
                {(!isMobile || (isMobile && selected === 'free')) && (
                  <div className="flex flex-col items-center justify-center rounded-[16px] bg-[#C7E5FF] px-7 py-5 text-center text-[#1E1E1E] lg:bg-[#EDF7FF]">
                    <h3 className="text-2xl leading-9 font-bold">
                      {dictionary.pricing.landing.package.package1.freeTrial}
                    </h3>
                    <div className="flex-1"></div>
                    <h1 className="text-[42px] leading-[50px] font-bold">
                      {dictionary.pricing.landing.package.package1.free}
                    </h1>
                    <p className="text-[12px] text-[#616669]">
                      {dictionary.pricing.landing.package.package1.desc}
                    </p>
                    <div className="flex-1"></div>
                    <a
                      href="https://scrmchampion.com/contact"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="block w-full cursor-pointer rounded-[50px] bg-[#F5CC00] px-4 py-2 text-sm whitespace-nowrap text-[#1E1E1E] hover:bg-[#C7E5FF]"
                    >
                      {dictionary.pricing.landing.package.package1.button}
                    </a>
                  </div>
                )}
                {(!isMobile || (isMobile && selected === 'standard')) && (
                  <div className="flex flex-col items-center justify-center rounded-[16px] bg-[#C7E5FF] px-7 py-5 text-center text-[#1E1E1E] lg:bg-[#EDF7FF]">
                    <h3 className="text-2xl leading-9 font-bold">
                      {dictionary.pricing.landing.package.package2.standard}
                    </h3>
                    <div className="flex flex-1 items-end">
                      <span>
                        {dictionary.pricing.landing.package.package2.from}
                      </span>
                    </div>
                    <h1 className="text-[42px] leading-[50px] font-bold">
                      {dictionary.pricing.landing.package.package2.price1}
                      <span className="text-lg font-normal">
                        {dictionary.pricing.landing.package.package2.port}
                      </span>
                    </h1>
                    <div className="flex flex-1 items-center gap-3">
                      <span className="line-through">
                        {dictionary.pricing.landing.package.package2.price2}
                      </span>
                      <span className="rounded-[50px] bg-[#1093FF] px-[12px] py-[1px] leading-[22px] font-bold text-white">
                        {dictionary.pricing.landing.package.package2.save}
                      </span>
                    </div>
                    <p className="text-[12px] text-[#616669]">
                      {dictionary.pricing.landing.package.package2.desc}
                    </p>
                    <div className="flex flex-1"></div>
                    <a
                      href="https://mall.007tg.com/"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-full cursor-pointer rounded-[50px] bg-[#F5CC00] px-4 py-2 text-sm whitespace-nowrap text-[#1E1E1E] hover:bg-[#C7E5FF]"
                    >
                      {dictionary.pricing.landing.package.package2.button}
                    </a>
                  </div>
                )}
                {(!isMobile || (isMobile && selected === 'professional')) && (
                  <div className="relative flex flex-col items-center justify-center rounded-br-[16px] rounded-bl-[16px] bg-[#C7E5FF] px-7 py-5 text-center text-[#1E1E1E] lg:bg-linear-[125deg,#00B6FF_0.64%,#0506DC_100%] lg:text-white">
                    <div className="absolute -top-[45px] right-0 left-0 h-[45px] rounded-tl-[16px] rounded-tr-[16px] bg-[#100B69] text-xl leading-[45px] font-bold text-white">
                      {dictionary.pricing.landing.package.package3.recommended}
                    </div>
                    <h3 className="text-2xl leading-9 font-bold">
                      {dictionary.pricing.landing.package.package3.professional}
                    </h3>
                    <div className="flex flex-1 items-end">
                      <span>
                        {dictionary.pricing.landing.package.package3.from}
                      </span>
                    </div>
                    <h1 className="text-[42px] leading-[50px] font-bold">
                      {dictionary.pricing.landing.package.package3.price1}
                      <span className="text-lg font-normal">
                        {dictionary.pricing.landing.package.package3.port}
                      </span>
                    </h1>
                    <div className="flex flex-1 items-center gap-3">
                      <span className="line-through">
                        {dictionary.pricing.landing.package.package3.price2}
                      </span>
                      <span className="rounded-[50px] bg-[#100B69] px-[12px] py-[1px] leading-[22px] font-bold text-white">
                        {dictionary.pricing.landing.package.package3.save}
                      </span>
                    </div>
                    <p className="text-[12px] text-[#1E1E1E] lg:text-white">
                      {dictionary.pricing.landing.package.package3.desc}
                    </p>
                    <div className="flex flex-1"></div>
                    <a
                      href="https://mall.007tg.com/"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="block w-full cursor-pointer rounded-[50px] bg-[#F5CC00] px-4 py-2 text-sm whitespace-nowrap text-[#1E1E1E] hover:bg-[#C7E5FF]"
                    >
                      {dictionary.pricing.landing.package.package3.button}
                    </a>
                  </div>
                )}
              </div>

              <div className="mt-14 mb-14 flex px-6 lg:hidden">
                <button className="w-full cursor-pointer rounded-[50px] bg-[#F5CC00] px-4 py-2 text-sm whitespace-nowrap text-[#1E1E1E] hover:bg-[#C7E5FF]">
                  {dictionary.pricing.landing.downloadPriceTable}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <TriggerAnimation>
        <div className="lg:animate-price-bottom-content hidden items-center justify-center bg-white lg:flex lg:opacity-0">
          <div className="w-full max-w-[1200px]">
            <div className="relative pb-[92px] lg:min-h-[810px]">
              <div className="-mt-[120px] mb-4 grid grid-cols-[2fr_1fr_1fr_1fr]">
                <div></div>
                {Object.keys(PRICES).map((key) => (
                  <div className="text-center font-bold text-white">
                    {dictionary.pricing.table.header[
                      key as keyof typeof dictionary.pricing.table.header
                    ] ?? key.toUpperCase()}
                  </div>
                ))}
              </div>

              {Object.entries(PRICES.free).map(([packKey, value]) => {
                const isObject = typeof value === 'object';

                if (isObject) {
                  return (
                    <div className="mb-4 grid auto-rows-[50px] grid-cols-[2fr_1fr_1fr_1fr] justify-center overflow-hidden rounded-2xl border border-[#1e1e1e]/30 text-black">
                      <div
                        className="col-span-4 flex cursor-pointer justify-between bg-[#EDF7FF] px-5 py-3 font-bold"
                        onClick={() => {
                          setOpen((prev) => {
                            return { ...prev, [packKey]: !prev[packKey] };
                          });
                        }}
                      >
                        {dictionary.pricing.table.dynamic[
                          packKey as keyof typeof dictionary.pricing.table.dynamic
                        ] ?? packKey}

                        <span className="self-end">
                          {open[packKey] === true ? <Minus /> : <Plus />}
                        </span>
                      </div>
                      {open[packKey] &&
                        Object.keys(value).map((valueKey, index) => {
                          const isLast =
                            index === Object.keys(value).length - 1;
                          return (
                            <>
                              <div
                                className={clsx(
                                  'flex items-center border-[#1e1e1e]/30 pl-5',
                                  !isLast && 'border-b',
                                )}
                              >
                                {dictionary.pricing.table.dynamic[
                                  valueKey as keyof typeof dictionary.pricing.table.dynamic
                                ] ?? valueKey}
                              </div>
                              {Object.values(PRICES).map((innerPack) => {
                                const packValue =
                                  innerPack[packKey as keyof typeof innerPack];
                                const finalValue =
                                  packValue[valueKey as keyof typeof packValue];

                                return (
                                  <div
                                    className={clsx(
                                      'flex items-center justify-center border-[#1e1e1e]/30 text-center text-sm',
                                      !isLast && 'border-b',
                                    )}
                                  >
                                    {finalValue === true && <Check />}
                                    {finalValue === false && <Dash />}
                                    {typeof finalValue !== 'boolean' && (
                                      <>
                                        <span
                                          dangerouslySetInnerHTML={{
                                            __html: removeIcon(
                                              dictionary.pricing.table.dynamic[
                                                finalValue as keyof typeof dictionary.pricing.table.dynamic
                                              ] ?? finalValue,
                                            ),
                                          }}
                                        ></span>
                                        <span>
                                          {withIcon(
                                            dictionary.pricing.table.dynamic[
                                              finalValue as keyof typeof dictionary.pricing.table.dynamic
                                            ] ?? finalValue,
                                          )}
                                        </span>
                                      </>
                                    )}
                                  </div>
                                );
                              })}
                            </>
                          );
                        })}
                    </div>
                  );
                }

                return (
                  <div className="mb-4 grid h-[50px] grid-cols-[2fr_1fr_1fr_1fr] rounded-2xl border border-[#1e1e1e]/30 bg-[#EDF7FF] px-5 py-3 text-black">
                    <div className="flex items-center font-bold">
                      {dictionary.pricing.table.dynamic[
                        packKey as keyof typeof dictionary.pricing.table.dynamic
                      ] ?? packKey}{' '}
                    </div>
                    {Object.values(PRICES).map((innerPack) => {
                      const finalValue =
                        innerPack[packKey as keyof typeof innerPack];

                      return (
                        <div className="flex items-center justify-center text-center text-sm">
                          {finalValue === true && <Check />}
                          {finalValue === false && <Dash />}
                          {typeof finalValue !== 'boolean' && (
                            <>
                              <span
                                dangerouslySetInnerHTML={{
                                  __html: removeIcon(
                                    dictionary.pricing.table.dynamic[
                                      finalValue as keyof typeof dictionary.pricing.table.dynamic
                                    ] ?? finalValue,
                                  ),
                                }}
                              ></span>
                              <span className="ml-2">
                                {withIcon(
                                  dictionary.pricing.table.dynamic[
                                    finalValue as keyof typeof dictionary.pricing.table.dynamic
                                  ] ?? finalValue,
                                )}
                              </span>
                            </>
                          )}
                        </div>
                      );
                    })}
                  </div>
                );
              })}

              <div
                ref={actionBarRef}
                className="z-50 grid w-full max-w-[1200px] auto-rows-[50px] grid-cols-[2fr_1fr_1fr_1fr] items-center justify-center gap-x-8 bg-white px-5 transition-all duration-300"
              >
                <div className="text-xl font-bold">
                  {dictionary.pricing.table.action.chooseYourPlan}
                </div>

                <a
                  href="https://scrmchampion.com/contact"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="m-auto block w-full max-w-[220px] cursor-pointer rounded-[50px] bg-[#F5CC00] px-4 py-2 text-center text-sm whitespace-nowrap text-[#1E1E1E] hover:bg-[#C7E5FF]"
                >
                  {dictionary.pricing.table.action.tryForFree}
                </a>
                <a
                  href="https://mall.007tg.com/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="m-auto block w-full max-w-[220px] cursor-pointer rounded-[50px] bg-[#F5CC00] px-4 py-2 text-center text-sm whitespace-nowrap text-[#1E1E1E] hover:bg-[#C7E5FF]"
                >
                  {dictionary.pricing.table.action.buyNow}
                </a>
                <a
                  href="https://mall.007tg.com/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="m-auto block w-full max-w-[220px] cursor-pointer rounded-[50px] bg-[#F5CC00] px-4 py-2 text-center text-sm whitespace-nowrap text-[#1E1E1E] hover:bg-[#C7E5FF]"
                >
                  {dictionary.pricing.table.action.buyNow}
                </a>
              </div>

              {isSticky && (
                <div className="fixed right-0 bottom-0 left-0 flex items-center justify-center bg-white">
                  <div className="z-50 grid w-full max-w-[1200px] grid-cols-[2fr_1fr_1fr_1fr] grid-rows-[30px_50px] items-center justify-center gap-x-8 bg-white px-5 transition-all duration-300">
                    <div></div>
                    {Object.keys(PRICES).map((key) => (
                      <div className="text-center font-bold text-[#1E1E1E]">
                        {dictionary.pricing.table.header[
                          key as keyof typeof dictionary.pricing.table.header
                        ] ?? key.toUpperCase()}
                      </div>
                    ))}
                    <div className="text-xl font-bold">
                      {' '}
                      {dictionary.pricing.table.action.chooseYourPlan}
                    </div>
                    <a
                      href="https://scrmchampion.com/contact"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="m-auto block w-full max-w-[220px] cursor-pointer rounded-[50px] bg-[#F5CC00] px-4 py-2 text-center text-sm whitespace-nowrap text-[#1E1E1E] hover:bg-[#C7E5FF]"
                    >
                      {dictionary.pricing.table.action.tryForFree}
                    </a>
                    <a
                      href="https://mall.007tg.com/"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="m-auto block w-full max-w-[220px] cursor-pointer rounded-[50px] bg-[#F5CC00] px-4 py-2 text-center text-sm whitespace-nowrap text-[#1E1E1E] hover:bg-[#C7E5FF]"
                    >
                      {dictionary.pricing.table.action.buyNow}
                    </a>
                    <a
                      href="https://mall.007tg.com/"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="m-auto block w-full max-w-[220px] cursor-pointer rounded-[50px] bg-[#F5CC00] px-4 py-2 text-center text-sm whitespace-nowrap text-[#1E1E1E] hover:bg-[#C7E5FF]"
                    >
                      {dictionary.pricing.table.action.buyNow}
                    </a>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </TriggerAnimation>
      <GetStarted dictionary={dictionary} locale={locale} />
    </>
  );
}
