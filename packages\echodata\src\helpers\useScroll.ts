'use client';

import { useEffect } from 'react';
import { create } from 'zustand';
interface ScrollState {
  isScrolled: boolean;
  setIsScrolled: (value: boolean) => void;
}

const useScrollStore = create<ScrollState>((set) => ({
  isScrolled: false,
  setIsScrolled: (value) => set({ isScrolled: value }),
}));

const useScroll = (offset: number = 50): boolean => {
  const { isScrolled, setIsScrolled } = useScrollStore();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > offset);
    };

    // Initial check
    handleScroll();

    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [isScrolled, offset, setIsScrolled]);

  return isScrolled;
};

export default useScroll;
