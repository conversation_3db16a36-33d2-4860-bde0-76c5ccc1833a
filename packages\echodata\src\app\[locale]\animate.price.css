:root {
  --price-animation-duration: 1.5s;
  --price-animation-half-duration: 0.75s;
}

@theme {
  --animate-price-top-content: priceTopContent var(--price-animation-duration)
    ease-in-out forwards;
  @keyframes priceTopContent {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }
  --animate-price-bottom-content: priceBottomContent
    var(--price-animation-duration) ease-in-out forwards;
  @keyframes priceBottomContent {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }
}
