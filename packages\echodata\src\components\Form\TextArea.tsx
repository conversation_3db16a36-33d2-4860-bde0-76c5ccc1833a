import Helper from '@hi7/components/Form/Helper';
import Label from '@hi7/components/Form/Label';
import { DEFAULT_INPUT_TYPE } from '@hi7/configs/input';
import { getInputClasses } from '@hi7/helpers/class';
import {
  getHelperOrder,
  getInputOrder,
  getLabelOrder,
} from '@hi7/helpers/input';
import type { InputProps } from '@hi7/interface/input';
import type React from 'react';
import type { ChangeEvent } from 'react';
import { useEffect, useState } from 'react';
import { useController, useFormContext } from 'react-hook-form';

type TextAreaProps = InputProps & {
  rows?: number;
};

const TextArea: React.FC<TextAreaProps> = ({
  label,
  name,
  placeholder = '',
  required,
  rows = 4,
  variant = DEFAULT_INPUT_TYPE,
}) => {
  const { control, trigger } = useFormContext();
  const { field } = useController({ name, control });
  const [isFocused, setIsFocused] = useState(false);
  const maxLength = 250;
  const [count, setCountCharacter] = useState('');

  useEffect(() => {
    // When form is reset, clear the count state
    if (field.value === '') {
      setCountCharacter('');
    }
  }, [field]);

  const handleBlur = () => {
    setIsFocused(false);
    trigger(name);
    field.onBlur();
  };

  const handleChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    field.onChange(e);
    if (e.target.value.length <= maxLength) {
      setCountCharacter(e.target.value);
    }
    trigger(name);
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  return (
    <div className="mb-5 flex flex-col gap-3">
      <div className="relative">
        <textarea
          maxLength={250}
          {...field}
          id={name}
          placeholder={placeholder}
          onBlur={handleBlur}
          onChange={handleChange}
          rows={rows}
          className={`${getInputClasses({ variant, value: field.value })} ${getInputOrder()}`}
          onFocus={handleFocus}
        />
        <Label
          name={name}
          required={required}
          isFocused={isFocused}
          variant={variant}
          value={field.value}
          classes={getLabelOrder()}
        >
          {label}
        </Label>

        <Helper name={name} classes={getHelperOrder()} />
      </div>
      <span className="text-hi7-gray text-end text-xs">
        {count.length} / {maxLength}
      </span>
    </div>
  );
};

export default TextArea;
