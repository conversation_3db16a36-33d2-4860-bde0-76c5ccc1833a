const { google } = require('googleapis');
const fs = require('fs');
const XLSX = require('xlsx');
const stream = require('stream');
require('dotenv').config();

// Convert grouped en.json and zh.json to Excel and upload to Google Drive
async function jsonToGoogleSheet(enJsonPath, zhJsonPath, recipientEmails) {
  const enData = JSON.parse(fs.readFileSync(enJsonPath, 'utf-8'));
  const zhData = JSON.parse(fs.readFileSync(zhJsonPath, 'utf-8'));

  // Function to flatten nested objects
  const flattenObject = (obj, prefix = '') =>
    Object.keys(obj).reduce((acc, key) => {
      const value = obj[key];
      const prefixedKey = prefix ? `${prefix}.${key}` : key;
      if (typeof value === 'object' && value !== null) {
        Object.assign(acc, flattenObject(value, prefixedKey));
      } else {
        acc[prefixedKey] = value;
      }
      return acc;
    }, {});

  // Create a new workbook
  const workbook = XLSX.utils.book_new();

  // Add data to workbook sheets
  for (const category in enData) {
    if (enData.hasOwnProperty(category) && zhData.hasOwnProperty(category)) {
      const flatCategoryEn = flattenObject(enData[category]);
      const flatCategoryZh = flattenObject(zhData[category]);

      const sheetData = Object.keys(flatCategoryEn).map((key) => ({
        Key: key,
        EN: flatCategoryEn[key] || '',
        ZH: flatCategoryZh[key] || '',
      }));

      const worksheet = XLSX.utils.json_to_sheet(sheetData);
      XLSX.utils.book_append_sheet(workbook, worksheet, category);
    }
  }

  // Convert workbook to buffer
  const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

  // Authenticate with Google API
  const auth = await authenticateGoogle();
  const drive = google.drive({ version: 'v3', auth });

  // Upload file to Google Drive
  const fileMetadata = {
    name: 'baymax_translation.xlsx', // Rename file when convert
    parents: ['root'],
  };

  const uploadStream = new stream.PassThrough();
  uploadStream.end(buffer);

  const media = {
    mimeType:
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    body: uploadStream, // Use stream to upload directly
  };

  let fileId;
  try {
    const response = await drive.files.create({
      resource: fileMetadata,
      media: media,
      fields: 'id',
    });
    fileId = response.data.id;
    console.log(`Google Sheet uploaded to Google Drive. File ID: ${fileId}`);
  } catch (error) {
    console.error('Failed to upload file:', error);
    throw error;
  }

  // After file is uploaded, set the general access
  await setGeneralAccess(fileId, drive);

  // Share the file with each recipient email
  for (const email of recipientEmails) {
    try {
      await drive.permissions.create({
        fileId,
        requestBody: {
          type: 'user',
          role: 'writer', // Options: 'reader', 'commenter', 'writer'
          emailAddress: email,
        },
      });
      console.log(`Google Sheet shared successfully to ${email}`);
    } catch (error) {
      console.error(`Failed to share file with ${email}:`, error);
    }
  }

  return fileId;
}

async function authenticateGoogle() {
  const credentials = {
    type: 'service_account',
    project_id: process.env.GOOGLE_PROJECT_ID,
    private_key_id: process.env.GOOGLE_PRIVATE_KEY_ID,
    private_key: process.env.GOOGLE_PRIVATE_KEY.replace(/\\n/g, '\n'),
    client_email: process.env.GOOGLE_CLIENT_EMAIL,
    client_id: process.env.GOOGLE_CLIENT_ID,
    auth_uri: 'https://accounts.google.com/o/oauth2/auth',
    token_uri: 'https://oauth2.googleapis.com/token',
    auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',
    client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.GOOGLE_CLIENT_EMAIL}`,
    universe_domain: 'googleapis.com',
  };

  const auth = new google.auth.GoogleAuth({
    credentials,
    scopes: ['https://www.googleapis.com/auth/drive.file'],
  });

  return auth.getClient();
}

// Set the file to be viewable by anyone in HiSeven Sdn Bhd
async function setGeneralAccess(fileId, drive) {
  try {
    await drive.permissions.create({
      fileId,
      requestBody: {
        type: 'domain',
        role: 'writer', // Options: 'reader', 'commenter', 'writer'
        domain: 'hiseven.com',
      },
    });
    console.log(
      'Google Sheet shared successfully with anyone in HiSeven Sdn Bhd.',
    );
  } catch (error) {
    console.error('Failed to shared with anyone in HiSeven Sdn Bhd:', error);
  }
}

// Paths for the files
const enJsonPath = '../src/dictionaries/en.json';
const zhJsonPath = '../src/dictionaries/zh.json';
const recipientEmails = ['<EMAIL>']; // Add email that u want to shared

jsonToGoogleSheet(enJsonPath, zhJsonPath, recipientEmails)
  .then(() => console.log('Google Sheet uploaded and shared successfully.'))
  .catch((error) => console.error('Failed to upload and share:', error));
