'use client';

import DropDown from '@hi7/assets/icon/dropdown.svg';
import Globe from '@hi7/assets/icon/globe.svg';
import { i18n, type Locale } from '@hi7/lib/i18n';
import { useGlobalStore } from '@hi7/provider/ZustandContext';
import clsx from 'clsx';
import { usePathname, useRouter } from 'next/navigation';
import { useRef, useState } from 'react';
import { DISPLAY_LANG, LANGUAGE_NAME } from './config';

export default function LocaleSwitcher() {
  const pathname = usePathname();
  const router = useRouter();
  const setBaseurl = useGlobalStore((s) => s.setBaseurl);

  // const [currentLocale, currentEndPoint] = pathname.split('/').slice(1) as [
  //   Locale,
  //   string,
  // ];

  // const handleLocaleChange = (locale: Locale) => {
  //   setBaseurl(`/${locale}`);
  //   router.push(`/${locale}${currentEndPoint ? `/${currentEndPoint}` : ''}`);
  // };

  const currentLocale = pathname.split('/')[1] as Locale;

  const handleLocaleChange = (locale: Locale) => {
    const segments = pathname.split('/');
    segments[1] = locale;
    const newPath = segments.join('/');
    setBaseurl(`/${locale}`);
    router.push(newPath);
  };

  const [isOpen, setIsOpen] = useState(false);
  const clearTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  function handleMouseEnter() {
    clearTimeoutRef.current && clearTimeout(clearTimeoutRef.current);
    setIsOpen(true);
  }

  function handleMouseLeave() {
    clearTimeoutRef.current && clearTimeout(clearTimeoutRef.current);
    clearTimeoutRef.current = setTimeout(() => {
      setIsOpen(false);
    }, 500);
  }

  return (
    <div
      // onClick={() => onToggle(open)}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      className={clsx('relative flex cursor-pointer items-center gap-2.5')}
    >
      <Globe />
      <div className="flex items-center gap-[10px]">
        <div className="flex items-center gap-[10px]">
          {DISPLAY_LANG[currentLocale] ?? currentLocale}
          <DropDown />
        </div>
      </div>

      {isOpen && (
        <div
          className={clsx(
            'rounded-[10px absolute top-full right-0 z-50 mt-7 h-fit min-h-fit w-fit overflow-hidden',
          )}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          {i18n.locales.map((locale) => (
            <button
              key={locale}
              onClick={() => handleLocaleChange(locale)}
              className={clsx(
                'flex items-center gap-2 whitespace-nowrap',
                'cursor-pointer text-start text-black',
                'px-[14px] py-4',
                'transition-all duration-300',
                'mb-2 w-full min-w-[155px] cursor-pointer rounded-[5px] bg-white hover:bg-[#C7E5FF]',
              )}
            >
              {LANGUAGE_NAME[locale] ?? locale}
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
