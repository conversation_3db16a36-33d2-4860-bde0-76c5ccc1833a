import client1 from '@hi7/assets/logo/client-1.png';
import client2 from '@hi7/assets/logo/client-2.png';
import client3 from '@hi7/assets/logo/client-3.png';
import client4 from '@hi7/assets/logo/h1.png';
import client5 from '@hi7/assets/logo/h2.png';
import type { StaticImageData } from 'next/image';

export type ClientKey = 'client1' | 'client2' | 'client3' | 'client4' | 'client5';

export const FEEDBACKS: { image: StaticImageData; key: ClientK<PERSON> }[] = [
  { image: client1, key: 'client1' },
  { image: client2, key: 'client2' },
  { image: client3, key: 'client3' },
  { image: client4, key: 'client4' },
  { image: client5, key: 'client5' },
];
