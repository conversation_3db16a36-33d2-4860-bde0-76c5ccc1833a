import Bg2 from '@hi7/assets/background/feature-2-bg-2.svg';
import Bg4 from '@hi7/assets/background/feature-2-bg-4.svg';
import Bg5 from '@hi7/assets/background/feature-2-bg-5.svg';
import Bg7 from '@hi7/assets/background/feature-2-bg-7.svg';
import type { DictionaryProps } from '@hi7/interface/i18n';

function Feature({ dictionary }: DictionaryProps) {
  return (
    <div className="flex items-center justify-center overflow-hidden bg-black text-white">
      <div className="w-full">
        <div className="relative min-h-[820px]">
          <div className="grid auto-rows-[210px] lg:auto-rows-[410px] lg:grid-cols-4">
            <video
              autoPlay
              muted
              loop
              className="order-1 h-[210px] w-full object-cover lg:h-[410px]"
            >
              <source
                src={`${process.env.NEXT_PUBLIC_ASSET_PREFIX}/videos/video-1.mp4`}
                type="video/mp4"
              />
            </video>
            <div className="relative order-2 flex flex-col overflow-hidden bg-[#1093FF] p-5 lg:px-8 lg:py-10">
              <div className="absolute bottom-0 left-0">
                <Bg2 />
              </div>
              <b className="relative text-[28px]">
                {dictionary.home.feature2.conversionRate.title}
              </b>
              <div className="flex-1"></div>
              <h5 className="relative text-8xl">
                {dictionary.home.feature2.conversionRate.desc}{' '}
                <span className="ml-[-12px] text-5xl">%</span>
              </h5>
            </div>
            <video
              autoPlay
              muted
              loop
              className="order-3 h-[210px] w-full object-cover lg:h-[410px]"
            >
              <source
                src={`${process.env.NEXT_PUBLIC_ASSET_PREFIX}/videos/video-2.mp4`}
                type="video/mp4"
              />
            </video>
            <div className="relative order-4 flex flex-col overflow-hidden bg-[#100B69] p-5 lg:px-8 lg:py-10">
              <div className="absolute bottom-[-20%] left-0 lg:bottom-0">
                <Bg4 />
              </div>
              <b className="relative text-[28px]">
                {dictionary.home.feature2.satisfactionRate.title}
              </b>
              <div className="flex-1"></div>
              <h5 className="relative text-8xl">
                {dictionary.home.feature2.satisfactionRate.desc}{' '}
                <span className="ml-[-12px] text-5xl">%</span>
              </h5>
            </div>
            <div className="relative order-6 flex flex-col overflow-hidden bg-[#000] p-5 lg:order-5 lg:px-8 lg:py-10">
              <div className="absolute bottom-[-20%] left-0 lg:bottom-0">
                <Bg5 />
              </div>
              <b className="relative text-[28px]">
                {dictionary.home.feature2.endClients.title}
              </b>
              <div className="flex-1"></div>
              <h5 className="relative text-8xl">
                {dictionary.home.feature2.endClients.desc}{' '}
                <span className="ml-[-12px] text-5xl">+</span>
              </h5>
            </div>
            <video
              autoPlay
              muted
              loop
              className="order-5 h-[210px] w-full object-cover lg:order-6 lg:h-[410px]"
            >
              <source
                src={`${process.env.NEXT_PUBLIC_ASSET_PREFIX}/videos/video-3.mp4`}
                type="video/mp4"
              />
            </video>
            <div className="relative order-8 flex flex-col overflow-hidden bg-[#F5CC00] p-5 text-black lg:order-7 lg:px-8 lg:py-10">
              <div className="absolute bottom-[-20%] left-0 lg:bottom-0">
                <Bg7 />
              </div>
              <b className="relative text-[28px]">
                {dictionary.home.feature2.activeUser.title}
              </b>
              <div className="flex-1"></div>
              <h5 className="relative text-8xl">
                {dictionary.home.feature2.activeUser.desc}{' '}
                <span className="ml-[-12px] text-5xl">+</span>
              </h5>
            </div>
            <video
              autoPlay
              muted
              loop
              className="order-7 h-[210px] w-full object-cover lg:order-8 lg:h-[410px]"
            >
              <source
                src={`${process.env.NEXT_PUBLIC_ASSET_PREFIX}/videos/video-4.mp4`}
                type="video/mp4"
              />
            </video>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Feature;
