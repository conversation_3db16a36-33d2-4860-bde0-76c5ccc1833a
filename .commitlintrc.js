module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    'type-empty': [0],
    'subject-empty': [0],
    'header-max-length': [2, 'always', 100],
    'hiseven-format': [2, 'always'],
  },
  helpUrl: '',
  plugins: [
    {
      rules: {
        'hiseven-format': ({ header }) => {
          // YesDev: [80195746] author: subject
          const yesDevRegex = /^\[\d+\] [^:]+: .+$/;
          const isValid = yesDevRegex.test(header);
          return [
            isValid,
            'The format should be - [{ticket}] {author}: {subject}',
          ];
        },
      },
    },
  ],
};
