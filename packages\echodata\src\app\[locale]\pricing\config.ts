export const PRICES = {
  free: {
    Ports: '5',
    'Multiple Account Login': {
      'Aggregate Multi-account Login': true,
      'Session Proxy': true,
      'Account Data Export': 'Max: 10 Entries',
      'Protocol Account Login': false,
      'Account List': false,
      'Device Management': false,
    },
    'Counting Features': {
      'Duplicate Fan Detection': false,
      'Traffic Distribution Link': false,
      'Duplicate Fan Tagging': false,
      'Data Work Order Report': false,
      'Work Order Deduplication': false,
    },
    'Translation Features': {
      'Text Translation': 'Lite',
      'Image Translation': false,
      'Voice Translation': false,
      'ChatGPT Translation': false,
      'Customer-Specific Translation': true,
    },
    'Sending Management': {
      'Personal Quick Replies': true,
      'Public Quick Replies': false,
      'Bulk Messaging': 'Limited (3 Conversations / Batch)',
      'Greeting Messages': false,
      'Keyword Auto-Reply': false,
    },
    'Message Management': {
      'Conversation Management': true,
      'Super Pinned Conversations': false,
      'Anti-Ban Settings': false,
      'Conversation Archive': false,
    },
    'Customer Management': {
      'Customer Profiling': false,
      'Follow-up Records': false,
      'Customer Tagging': false,
      'Fan Activity Tagging': false,
      'Customer List': false,
      'Customer Statistics': false,
    },
    'Internal Control Management': {
      'Customer Data Encryption': false,
      'Chat Monitoring': false,
      'Sensitive Word Monitoring': false,
      'Sensitive Behaviour Monitoring': false,
      'Decryption Key': false,
      'Session Statistics': false,
      'Private Key': false,
    },
    'Community Management': {
      'Group List': false,
      'Group Tag Management': false,
      'Group Welcome Message': false,
      'Group Bot': true,
    },
    'Organisation Management': {
      'Role Management': false,
      'Organisational Structure': false,
    },
    'Performance Management': {
      'Performance Settings': false,
      'Performance Reports': false,
    },
    'Webhook Management': false,
    'Task & Schedule Management': false,
    'Voice Marketing': false,
    'Email Management': false,
    Dashboard: false,
  },
  standard: {
    Ports: '5 - 100 and above <Info />',
    'Multiple Account Login': {
      'Aggregate Multi-account Login': true,
      'Session Proxy': true,
      'Account Data Export': 'Max: 10 Entries',
      'Protocol Account Login': true,
      'Account List': true,
      'Device Management': true,
    },
    'Counting Features': {
      'Duplicate Fan Detection': true,
      'Traffic Distribution Link': true,
      'Duplicate Fan Tagging': false,
      'Data Work Order Report': true,
      'Work Order Deduplication': true,
    },
    'Translation Features': {
      'Text Translation': 'Standard',
      'Image Translation': 'Limited (10 / day)',
      'Voice Translation': 'Limited (10 / day)',
      'ChatGPT Translation': false,
      'Customer-Specific Translation': true,
    },
    'Sending Management': {
      'Personal Quick Replies': true,
      'Public Quick Replies': true,
      'Bulk Messaging': 'Limited (3 Conversations / Batch)',
      'Greeting Messages': true,
      'Keyword Auto-Reply': 'First 3 keywords only (unlimited replies)',
    },
    'Message Management': {
      'Conversation Management': true,
      'Super Pinned Conversations': false,
      'Anti-Ban Settings': false,
      'Conversation Archive':
        'Chat history: First 20 messages only, <br/> 10 replies per client/day',
    },
    'Customer Management': {
      'Customer Profiling': true,
      'Follow-up Records': true,
      'Customer Tagging': true,
      'Fan Activity Tagging': false,
      'Customer List':
        'Client data: First 20 only <br/> Chat history: First 20 messages only',
      'Customer Statistics': false,
    },
    'Internal Control Management': {
      'Customer Data Encryption': false,
      'Chat Monitoring': 'Chat History: First 20 Messages',
      'Sensitive Word Monitoring': 'Limited to First 10 Sensitive Word',
      'Sensitive Behaviour Monitoring':
        'Limited to First 10 Sensitive Behaviour',
      'Decryption Key': false,
      'Session Statistics': false,
      'Private Key': true,
    },
    'Community Management': {
      'Group List':
        'Group data: First 20 Group only <br/> Chat history: First 20 messages only',
      'Group Tag Management': false,
      'Group Welcome Message': false,
      'Group Bot': true,
    },
    'Organisation Management': {
      'Role Management': false,
      'Organisational Structure': false,
    },
    'Performance Management': {
      'Performance Settings': false,
      'Performance Reports': false,
    },
    'Webhook Management': false,
    'Task & Schedule Management': true,
    'Voice Marketing': false,
    'Email Management': true,
    Dashboard: true,
  },
  professional: {
    Ports: '5 - 100 and above <Info />',
    'Multiple Account Login': {
      'Aggregate Multi-account Login': true,
      'Session Proxy': true,
      'Account Data Export': 'Unlimited',
      'Protocol Account Login': true,
      'Account List': true,
      'Device Management': true,
    },
    'Counting Features': {
      'Duplicate Fan Detection': true,
      'Traffic Distribution Link': true,
      'Duplicate Fan Tagging': true,
      'Data Work Order Report': true,
      'Work Order Deduplication': true,
    },
    'Translation Features': {
      'Text Translation': 'Pro',
      'Image Translation': 'Unlimited',
      'Voice Translation': 'Unlimited',
      'ChatGPT Translation': true,
      'Customer-Specific Translation': true,
    },
    'Sending Management': {
      'Personal Quick Replies': true,
      'Public Quick Replies': true,
      'Bulk Messaging': 'Unlimited Messages',
      'Greeting Messages': true,
      'Keyword Auto-Reply': true,
    },
    'Message Management': {
      'Conversation Management': true,
      'Super Pinned Conversations': true,
      'Anti-Ban Settings': true,
      'Conversation Archive': 'All Messages',
    },
    'Customer Management': {
      'Customer Profiling': true,
      'Follow-up Records': true,
      'Customer Tagging': true,
      'Fan Activity Tagging': true,
      'Customer List':
        'Client data: ALL Client Data <br/> Chat history: All Message History',
      'Customer Statistics': true,
    },
    'Internal Control Management': {
      'Customer Data Encryption': true,
      'Chat Monitoring': 'All Messages',
      'Sensitive Word Monitoring': 'Unlimited Sensitive Keywords',
      'Sensitive Behaviour Monitoring': 'Unlimited Sensitive Behaviours',
      'Decryption Key': true,
      'Session Statistics': true,
      'Private Key': true,
    },
    'Community Management': {
      'Group List': 'Group data: Unlimited <br/> Chat history: Unlimited',
      'Group Tag Management': true,
      'Group Welcome Message': true,
      'Group Bot': true,
    },
    'Organisation Management': {
      'Role Management': true,
      'Organisational Structure': true,
    },
    'Performance Management': {
      'Performance Settings': true,
      'Performance Reports': true,
    },
    'Webhook Management': true,
    'Task & Schedule Management': true,
    'Voice Marketing': true,
    'Email Management': true,
    Dashboard: true,
  },
} as const;
