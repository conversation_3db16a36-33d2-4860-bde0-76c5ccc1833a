:root {
  --product-animation-duration: 1.5s;
  --product-animation-half-duration: 0.75s;
}

@theme {
  --animate-product-2-picture: product2Picture var(--product-animation-duration)
    ease-in-out forwards;
  @keyframes product2Picture {
    0% {
      opacity: 0;
      transform: translate(-25%, 100%);
    }

    100% {
      opacity: 1;
      transform: translate(0, -10%);
    }
  }

  --animate-product-2-bg-left: product2BgLeft var(--product-animation-duration)
    ease-in-out forwards;
  @keyframes product2BgLeft {
    0% {
      transform: translateY(-100%);
    }

    100% {
      transform: translateY(0);
    }
  }

  --animate-product-2-content: product2Content var(--product-animation-duration)
    ease-in-out forwards;
  @keyframes product2Content {
    50% {
      opacity: 0;
      transform: translateY(-50%);
    }

    100% {
      opacity: 1;
      transform: translateY(50%);
    }
  }

  --animate-product-2-bg-right: product2BgRight
    var(--product-animation-duration) ease-in-out forwards;
  @keyframes product2BgRight {
    0% {
      transform: rotate(-45deg) translateX(150%);
    }

    100% {
      transform: rotate(0deg) translateX(0);
    }
  }

  --animate-product-2-bottom-ring-left: product2BottomRingLeft
    var(--product-animation-duration) ease-in-out forwards;
  @keyframes product2BottomRingLeft {
    0% {
      opacity: 0;
      transform: rotate(-45deg) translateY(150%);
    }

    100% {
      opacity: 1;
      transform: rotate(0deg) translateY(0);
    }
  }

  --animate-product-2-bottom-ring-right: product2BottomRingRight
    var(--product-animation-duration) ease-in-out forwards;
  @keyframes product2BottomRingRight {
    0% {
      opacity: 0;
      transform: rotate(45deg) translateY(150%);
    }

    100% {
      opacity: 1;
      transform: rotate(0deg) translateY(0);
    }
  }

  --animate-product-2-bottom-title: product2BottomTitle
    var(--product-animation-duration) ease-in-out forwards;
  @keyframes product2BottomTitle {
    0% {
      opacity: 0;
      transform: translateY(-100%) scale(0.8);
    }

    100% {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  --animate-product-2-bottom-content: product2BottomContent
    var(--product-animation-duration) ease-in-out forwards;
  @keyframes product2BottomContent {
    0%,
    50% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }
}
