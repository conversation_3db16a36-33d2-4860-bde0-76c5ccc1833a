import AppIntergration from '@hi7/components/Home/AppIntegration';
import BubbleElement from '@hi7/components/Home/BubbleElement';
import FeatureOne from '@hi7/components/Home/FeatureOne';
import FeatureTwo from '@hi7/components/Home/FeatureTwo';
import GetStarted from '@hi7/components/Home/GetStarted';
import Landing from '@hi7/components/Home/Landing';
import Testimonial from '@hi7/components/Home/Testimonial';
import WhyChooseUs from '@hi7/components/Home/WhyChooseUs';
import type { Dictionary } from '@hi7/interface/dictionary';
import { getDictionary, type Locale } from '@hi7/lib/i18n';

type PageProps = {
  params: {
    locale: Locale;
  };
};

export default async function Page({ params }: PageProps) {
  const { locale } = params;
  const t: Dictionary = await getDictionary(locale);

  return (
    <>
      <Landing dictionary={t} />
      <BubbleElement dictionary={t} />
      <AppIntergration dictionary={t} />
      <WhyChooseUs dictionary={t} />
      <FeatureOne dictionary={t} />
      <FeatureTwo dictionary={t} />
      <Testimonial dictionary={t} />
      <GetStarted dictionary={t} />
    </>
  );
}
