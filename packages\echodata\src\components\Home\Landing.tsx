
'use client';

import HomepageBgMobile1 from '@hi7/assets/background/homepage-bg1-mobile.png';
import HomepageBg1 from '@hi7/assets/background/homepage-bg1.png';
import HomepageBgMobile2 from '@hi7/assets/background/homepage-bg2-mobile.png';
import HomepageBg2 from '@hi7/assets/background/homepage-bg2.png';
import HomepageBgMobile3 from '@hi7/assets/background/homepage-bg3-mobile.png';
import HomepageLongdVector from '@hi7/assets/icon/homepage-long-vector.png';
import HomepageRoundVector from '@hi7/assets/icon/homepage-round-vector.png';

import RoundChevronLeft from '@hi7/assets/icon/round-chevron-left.svg';
import RoundChevronRight from '@hi7/assets/icon/round-chevron-right.svg';
import type { DictionaryProps } from '@hi7/interface/i18n';
import { Arsenal } from 'next/font/google';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import { useSwipeable } from 'react-swipeable';

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
  display: 'swap',
});

function Landing({ dictionary }: DictionaryProps) {
  const slides = [
    {
      title: 'The Global Leader in\nPrecise Number Filtering',
      subtitle: 'Manage Global Customers and Integrated Accounts on One\nPowerful Platform',
      significantText1: ' 200+',
      significantText2: ' 60+',
      desktopImage: HomepageBg1,
      mobileImage: HomepageBgMobile1,
    },
    {
      title: 'Lorem lpsum Dolor Sit\nAmet Aenean Massa',
      subtitle: 'Quisque rutrum. Aenean imperdiet etiam ultricies nisi vel augue\ncurabitur ullamcorper ultricies nisi',
      significantText1: ' 200+',
      significantText2: ' 60+',
      desktopImage: HomepageBg2,
      mobileImage: HomepageBgMobile2,
    },
    {
      title: 'Lorem lpsum Dolor Sit\nAmet Aenean Massa',
      subtitle: 'Quisque rutrum. Aenean imperdiet etiam ultricies nisi velaugue\ncurabitur ullamcorper ultricies nisi',
      significantText1: ' 200+',
      significantText2: ' 60+',
      desktopImage: HomepageBg2,
      mobileImage: HomepageBgMobile3,
    },
  ];

  const handlers = useSwipeable({
    onSwipedLeft: () => nextSlide(),
    onSwipedRight: () => prevSlide(),
    preventScrollOnSwipe: true,
    trackMouse: true,
  });

  const [currentSlide, setCurrentSlide] = useState(0);
  const [animate, setAnimate] = useState(false);

  const triggerAnimation = () => {
    setAnimate(false);
    setTimeout(() => setAnimate(true), 10); // Re-trigger animation
  };
  const nextSlide = () => {
    triggerAnimation();
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  const prevSlide = () => {
    triggerAnimation();
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };

  const current = slides[currentSlide];

  useEffect(() => {
    const interval = setInterval(() => {
      nextSlide();
    }, 5000);

    return () => clearInterval(interval);
  }, [currentSlide]);

  return (
    <div className="relative flex items-center justify-center overflow-hidden lg:mt-[-80px]" {...handlers}>
      <div className="w-full">
        <div className="h-screen">
          {/******* Mobile *******/}
          <div className="relative h-[95%] lg:hidden block">
            <div className="relative h-full w-full transition-all duration-700 ease-in-out">
              <Image
                fill
                src={current.mobileImage}
                alt={current.title}
                className="object-cover"
                priority
              />
            </div>

            {/******* Text Overlay *******/}
            <div className={`absolute top-10 w-full text-start text-white px-4 z-10 transition-opacity duration-700 ${animate && 'animate-slide-in-left'}`}>
              <h1 className={`text-[40px] font-bold drop-shadow-md mb-3 leading-[42px] ${arsenal.className}`}>{current.title}</h1>
              <p className='text-md font-thin mb-3'>{current.subtitle}</p>
              <hr className='mb-3' />
              <h3 className='mb-3'>
                <span className="font-light align-top text-[14px]">{dictionary.home.landing.supportedIn}</span>
                <span className="font-bold text-[33px] align-top">{current.significantText1}</span>
                <span className="font-thin text-[23px] align-bottom"> {dictionary.home.landing.countries}</span>
              </h3>
              <hr className='mb-3' />
              <h3 className='mb-3'>
                <span className="font-light align-top text-[14px]">{dictionary.home.landing.integratedWith} </span>
                <span className="font-bold text-[33px] align-top">{current.significantText2}</span>
                <span className="font-thin text-[23px] align-bottom"> {dictionary.home.landing.appPlatforms}</span>
              </h3>
              <hr className='mb-6' />
              <a
                href="#"
                target="_blank"
                className="block w-auto max-w-[200px] text-center cursor-pointer rounded-[25px] bg-[#FF5542] py-0 px-[30px] whitespace-nowrap text-white hover:bg-[#C7E5FF] font-bold text-[18px] leading-[40px]"
              >
                {dictionary.general.freeTrial.button1}
              </a>
            </div>

            {/******* Slide Indicators *******/}
            <div className="absolute bottom-14 right-8 w-full flex justify-end gap-3 z-20">
              {slides.map((slide, idx) => (
                <span
                  key={idx}
                  style={{
                    backgroundColor: idx === currentSlide ? '#fc5444' : '#047aff',
                  }}
                  className="size-4 rounded-full"
                />
              ))}
            </div>
          </div>

          {/****** Desktop ******/}
          <div className="relative hidden lg:flex lg:h-[85%] items-center justify-center">
            <Image
              width={1920}
              src={current.desktopImage}
              alt={current.title}
              className="object-cover"
            />
            <div className={`absolute left-17 top-30 xl:left-24 xl:top-35 z-10 text-white text-start whitespace-break-spaces transition-opacity duration-700 ${animate && 'animate-slide-in-left'}`}>
              <h1 className={`text-[48px] font-bold leading-[55px] tracking-[1px] mb-5 xl:text-[64px] xl:leading-[68px] xl:mb-9 ${arsenal.className}`}>{current.title}</h1>
              <p className='text-[24px] font-thin mb-3 leading-[30px] xl:mb-10'>{current.subtitle}</p>
              <hr className='mb-5 w-[620px] xl:w-[950px] xl:mb-3' />
              <h3 className='mb-0 xl:mb-7'>
                <span className="font-[500] text-[20px] xl:text-[24px]">{dictionary.home.landing.supportedIn}</span>
                <span className="font-bold text-[40px] xl:text-[48px]">{current.significantText1}</span>
                <span className="font-thin text-[40px] xl:text-[48px]"> {dictionary.home.landing.countries}</span>
              </h3>
              <hr className='mb-3 w-[620px] xl:w-[950px]' />
              <h3 className='mb-0 xl:mb-7'>
                <span className="font-[500] text-[20px] xl:text-[24px]">{dictionary.home.landing.integratedWith} </span>
                <span className="font-bold text-[40px] xl:text-[48px]">{current.significantText2}</span>
                <span className="font-thin text-[40px] xl:text-[48px]"> {dictionary.home.landing.appPlatforms}</span>
              </h3>
              <hr className='mb-6 w-[620px] xl:w-[950px] xl:mb-10' />
              <a
                href="#"
                target="_blank"
                className="block w-auto max-w-[200px] text-center cursor-pointer rounded-[25px] bg-[#FF5542] py-0 px-[30px] whitespace-nowrap text-white hover:bg-[#C7E5FF] font-bold text-[18px] leading-[40px]"
              >
                {dictionary.general.freeTrial.button1}
              </a>
            </div>

            {/* Navigation Arrows */}
            <div className="absolute bottom-1/7 right-2/11 md:bottom-1/6 md:right-1/5 lg:bottom-1/6 lg:right-1/7 z-20 flex xl:right-1/6 xl:bottom-1/5">
              <RoundChevronLeft className="" onClick={prevSlide} />
              <RoundChevronRight className="" onClick={nextSlide} />
            </div>

          </div>
          <div className="z-20 hidden lg:flex w-full">
            <Image
              src={HomepageRoundVector}
              alt="icon"
              className="absolute bottom-0 right-1/9 w-[22%] h-[22%] object-contain"
            />
            <Image
              src={HomepageLongdVector}
              alt="icon"
              className="absolute -bottom-10 -right-15 scale-90 xl:scale-130 xl:bottom-0 xl:right-0"
            />

          </div>
        </div>
      </div>
    </div >
  );
}

export default Landing;
