'use client';
import HomepageSection2Img from '@hi7/assets/background/homepage-section2-img.jpg';
import SupportedPlatforms from '@hi7/components/SupportedPlatforms';
import type { DictionaryProps } from '@hi7/interface/i18n';
import clsx from 'clsx';
import { Arsenal } from 'next/font/google';
import Image from 'next/image';
import TriggerAnimation from '../TriggerAnimation';

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
  display: 'swap',
});

function AppIntergration({ dictionary }: DictionaryProps) {
  return (
    <TriggerAnimation>

      <div className="relative flex flex-col md:flex-col lg:flex-col-reverse">
        <section className="pb-20 lg:pb-0 xl:pt-10 rounded-r-[40px] lg:rounded-r-[100px] xl:rounded-r-[130px] relative h-[500px] md:h-[480px] lg:h-screen xl:h-[875px] mt-0 lg:-mt-65 xl:-mt-90">
          <div className="bg-[#90c4fc] pl-0 pt-12 lg:pt-12 xl:pt-6 pr-6 lg:bg-transparent lg:pr-0 xl:pr-0 text-right">
            <div className='mb-9 lg:mb-8 xl:mb-9 lg:hidden'><h1 className={`text-[42px] lg:text-[64px] font-semibold text-[#0F172A] tracking-[1px] ${arsenal.className}`}>App Integration</h1></div>
            <p className="text-[18px] lg:text-[19px] xl:text-3xl text-gray-700 mt-2 xl:mt-0 mb-5 lg:mb-23 xl:mb-42 leading-[25px] lg:hidden xl:leading-[40px] tracking-[0px] text-thin">
              Access High-quality Marketing Data <br />
              Effortlessly with <strong>60+ Popular Platforms</strong>
              <br /> and<strong>  Applications </strong>integration
            </p>
            <div className="hidden lg:block">
              <div className="flex flex-row md:flex-row h-auto lg:h-[50%]">
                <div className="relative w-full w-1/2 md:w-2/3 lg:w-5/9">
                  <Image
                    src={HomepageSection2Img}
                    alt={'insight-img'}
                    className={clsx(
                      'mt-0 border-r-25 border-b-25 border-t-25 rounded-r-[45px] border-white object-cover h-[30vh]',
                      'md:mr-[140px] md:border-l-25 md:border-r-0 md:rounded-[70px] md:rounded-r-none',
                      'lg:h-[50vh] lg:translate-y-[-5%] lg:-ml-0 lg:border-r-[45px] lg:rounded-r-[85px] lg:rounded-l-[0px] lg:mx-0 lg:border-l-[0px] lg:border-b-[40px]',
                      'xl:h-[47vh] xl:translate-y-[-20%]',
                    )}
                  />
                </div>
                <div
                  className={clsx(
                    'mb-40 w-full bg-[#90c4fc] rounded-b-[40px] mt-20',
                    'md:-mb-10 md:w-1/3 md:pr-10 md:mb-0',
                    'lg:w-4/9 lg:rounded-t-[70px] lg:items-start lg:justify-start lg:pr-15 lg:pt-10 lg:-mb-20',
                    'xl:pl-20'
                  )}
                >
                  <div className='mb-9 lg:mb-5 xl:mb-9'><h1 className={`text-[42px] lg:text-[60px] font-semibold text-[#0F172A] tracking-[1px] ${arsenal.className}`}>App Integration</h1></div>
                  <p className="text-[18px] text-gray-700 mt-2 mb-5 lg:mb-23 leading-[25px] tracking-[0px] text-thin xl:leading-[32px] xl:mb-42 xl:mt-0 xl:text-2xl ">
                    Access High-quality Marketing Data <br />
                    Effortlessly with <strong>60+ Popular Platforms</strong>
                    <br /> and<strong>  Applications </strong>integration
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-[#90c4fc] pb-20 flex flex-wrap justify-center gap-4 mt-4 lg:py-25 lg:pr-20 lg:-mt-25 xl:-mt-[180px] xl:pr-20">
              <div className="relative absolute left-0 top-0 h-full w-full lg:top-10">
                <hr className='my-2 lg:mt-2 lg:mb-0 xl:mb-5' />
                <SupportedPlatforms location="1st" dictionary={dictionary} />
                <hr className='my-2 lg:mt-2 lg:mb-0 xl:mb-5' />
                <SupportedPlatforms location="2nd" dictionary={dictionary} />
                <hr className='my-2 lg:mt-2 xl:mt-3 lg:mb-0' />
              </div>
            </div>
          </div>
        </section>

        <section className="-mt-10 lg:hidden lg:mt-0">
          <div className="overflow-hidden">
            <div className="flex flex-row md:flex-row h-auto md:h-[50%] lg:h-[130px]">
              <div className="relative w-[100vw] w-1/2 md:w-2/3 lg:w-5/9">
                <Image
                  src={HomepageSection2Img}
                  alt={'insight-img'}
                  className={clsx(
                    'mt-0 border-r-25 border-b-25 border-t-25 rounded-r-[45px] border-white object-cover h-[40vh]',
                    'md:h-[35vh] md:w-full md:border-r-25 md:rounded-[70px] md:rounded-l-none',
                    'lg:h-[50vh] lg:translate-y-[-5%] lg:-ml-0 lg:border-r-[45px] lg:rounded-[85px] lg:mx-50 lg:border-l-35 lg:border-b-30',
                    'xl:h-[45vh] xl:translate-y-[-20%]',
                  )}
                />
              </div>
              <div
                className={clsx(
                  'mb-60 w-full bg-[#90c4fc] rounded-b-[40px] items-end flex justify-end z-10',
                  'md:w-1/3 md:mb-50',
                  'lg:w-4/9 lg:rounded-t-[70px] lg:items-end lg:justify-start lg:pr-0 lg:pl-16 lg:-mb-27',
                  'xl:pl-20'
                )}
              >
              </div>
            </div>
          </div>
        </section>
      </div>

    </TriggerAnimation>
  );
}

export default AppIntergration;
