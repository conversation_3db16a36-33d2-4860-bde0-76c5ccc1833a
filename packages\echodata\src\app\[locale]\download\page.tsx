import productBgCenter from '@hi7/assets/background/download-bg-1.png';
import productBgLeft from '@hi7/assets/background/download-bg-2.png';
import ProductBgTopLeft1 from '@hi7/assets/background/download-bg-top-left-1.svg';
import ProductBgTopLeft2 from '@hi7/assets/background/download-bg-top-left-2.svg';
import ProductBgTopRight from '@hi7/assets/background/download-bg-top-right.svg';
import MacOs from '@hi7/assets/icon/macos.svg';
import Windows from '@hi7/assets/icon/windows.svg';
import SupportedPlatforms from '@hi7/components/SupportedPlatforms';

import type { Dictionary } from '@hi7/interface/dictionary';
import type { LocaleProps } from '@hi7/interface/i18n';
import { getDictionary } from '@hi7/lib/i18n';
import Image from 'next/image';

async function page({ params }: LocaleProps) {
  const { locale } = params;
  const dictionary: Dictionary = await getDictionary(locale);
  return (
    <div className="flex items-center justify-center overflow-hidden bg-linear-[180deg,#172DB1_0%,#90C6FF_50%,#E1EFFF_100%]">
      <div className="w-full">
        <div className="relative lg:min-h-dvh">
          <div className="lg:animate-download-bg-right absolute top-[250px] right-[-120px] lg:top-[80px] lg:right-0 lg:h-dvh lg:opacity-0">
            <ProductBgTopRight height="100%" />
          </div>
          <div className="lg:animate-download-bg-left-1 absolute top-[-20px] left-[-80px] lg:top-[-5dvh] lg:left-[-5dvw] lg:z-0 lg:h-dvh lg:translate-x-[-100%]">
            <ProductBgTopLeft1 height="100%" />
          </div>
          <div className="lg:animate-download-bg-left-2 absolute top-[-20px] left-[-80px] lg:top-[-5dvh] lg:left-[-5dvw] lg:z-0 lg:h-[99dvh] lg:translate-x-[-100%] lg:opacity-0">
            <ProductBgTopLeft2 height="100%" />
          </div>

          <div className="relative flex flex-col pt-16 text-white lg:flex-row lg:pt-[20dvh] lg:pl-[120px]">
            <div className="lg:animate-download-title flex w-full flex-1 flex-col px-9 text-center lg:translate-x-[-120%] lg:px-0 lg:text-left lg:opacity-0">
              <h1
                className="mb-5 text-[40px] leading-[48px] font-bold lg:text-[50px] lg:leading-[55px]"
                dangerouslySetInnerHTML={{ __html: dictionary.download.title }}
              />

              <h3 className="text-[20px] leading-7">
                {dictionary.download.desc}
              </h3>
              <div className="flex flex-col items-center lg:flex-row lg:items-start">
                <a
                  href="https://admin.scrmchampion.com/client-download"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="mt-4 flex w-max cursor-pointer flex-row items-center justify-center gap-2 rounded-[50px] bg-[#F5CC00] px-6 py-3 whitespace-nowrap text-[#1E1E1E] hover:bg-[#C7E5FF]"
                >
                  <Windows />
                  <span>{dictionary.download.windowsButton}</span>
                </a>
                <a
                  href="https://admin.scrmchampion.com/client-download"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="mt-4 ml-4 flex w-max cursor-pointer flex-row items-center justify-center gap-2 rounded-[50px] bg-[#F5CC00] px-6 py-3 whitespace-nowrap text-[#1E1E1E] hover:bg-[#C7E5FF]"
                >
                  <MacOs />
                  <span>{dictionary.download.masOsButton}</span>
                </a>
              </div>
            </div>

            <div className="lg:animate-download-picture lg:bm-0 relative mb-10 h-[50dvh] lg:mt-[-12dvh] lg:h-[55dvh] lg:w-[40dvw] lg:flex-[1.1]">
              <div className="absolute h-[50dvh] w-[80%] lg:top-[-60px] lg:my-0 lg:h-[55dvh] lg:w-[40dvw]">
                <Image
                  fill
                  src={productBgLeft}
                  alt={''}
                  className="object-contain"
                />
              </div>

              <div className="absolute top-[85px] right-0 h-[50dvh] w-[80%] lg:top-[75px] lg:left-[120px] lg:my-0 lg:h-[55dvh] lg:w-[40dvw]">
                <Image
                  fill
                  src={productBgCenter}
                  alt={''}
                  className="object-contain"
                />
              </div>
            </div>
          </div>

          <div className="lg:animate-download-slider absolute right-0 bottom-[5dvh] left-0 lg:opacity-0">
            <SupportedPlatforms location="download" dictionary={dictionary} />
          </div>
        </div>
      </div>
    </div>
  );
}

export default page;
