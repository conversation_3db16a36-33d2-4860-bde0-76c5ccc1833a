#!/bin/bash

rm -f ./.env

case "$1" in
    dev)
        echo "Development environment selected"
        cp ./env.development ./.env
        ;;
    stg)
        echo "Staging environment selected"
        cp ./env.staging ./.env
        ;;
    prod)
        echo "Production environment selected"
        cp ./env.production ./.env
        ;;
    *)
        echo "Invalid environment. Please select dev, stg, or prod."
        exit 1
        ;;
esac

next dev -p 4101 -H local.echodata.hiseven.com --experimental-https