export const SUGGESTIONS = [
  'counter',
  'translation',
  'multiAccountLogin',
  'internalControlManagement',
  'dataReports',
  'gptTranslation',
  'duplicateFanDetection',
] as const;

export const FAQ = {
  'What is SCRM Champion?': [
    'SCRM Champion is a comprehensive and extensive CRM system focused on customer management. It helps users break language barriers for seamless global communication.',
  ],
  'What can SCRM Champion do for me?': [
    'Provides an all-in-one management platform, allowing real-time team management, data monitoring, and other admin tools.',
    'Enhances operational efficiency, enabling precise fan and customer segmentation, supporting customer acquisition and direct connections.',
    'Offers intelligent marketing tools, facilitating faster and more accurate transactions to boost sales growth.',
    'Real-time translation, supporting 10+ translation engines and 200+ languages, making communication effortless.',
    'Manages multiple platforms and accounts simultaneously, improving efficiency and expanding your global reach.',
  ],
  'What is Aggregate Multi-Account Login?': [
    'Multi-Instance Aggregation allows users to log into multiple accounts from different platforms on a desktop device. Users can seamlessly switch between these accounts and platforms. Additionally, a single PC device can log into up to five desktop instances simultaneously.',
  ],
  'What is System Duplicate Detection?': [
    `This feature compares specified work order fans with SCRM Champion's large-scale data from the past three months to analyze overall duplication rates.`,
  ],
  'What is Sensitive Word Monitoring?': [
    `The system can monitor work order chat room messages using a specific activation code in the backend. If sensitive words appear, the system will log them.`,
    `If a sensitive word is set to 'Prohibited', messages containing it cannot be sent. If a sensitive word is set to 'Allowed', messages containing it will be sent, but they will be logged in the backend.`,
    `Whether the content is sent directly or translated first before sending, the system will detect and trigger the sensitive word filter in the chatroom.`,
  ],
  'What Translation Types Are Supported?': [
    'Real-time two-way translation, Image translation, Voice translation, Human translation',
  ],
} as const;

export type ThreadKey = 'threads1' | 'threads2' | 'threads3';

export const THREADS: { key: ThreadKey }[] = [
  { key: 'threads1' },
  { key: 'threads2' },
  { key: 'threads3' },
];

// export const THREADS = [
//   {
//     title: 'Attendance Management',
//     description:
//       'Flexible attendance management with customizable rules, automatic clock-in tracking, and multi-shift support, enabling easy employee access via desktop sub-accounts.',
//     updatedAt: '13 FEBRUARY 2025',
//   },
//   {
//     title: 'AI Intelligent Customer Service Management',
//     description:
//       'Integrates with third-party APIs (Dify platform) for desktop chat, featuring assisted mode to boost efficiency and reduce manual response costs.',
//     updatedAt: '9 JANURAY 2025',
//   },
//   {
//     title: 'Analysis of New Member Growth',
//     description:
//       'Automatically tracks and analyzes key metrics such as new group members, net member growth, and returning customers within a specified time period.',
//     updatedAt: '2 JANURAY 2025',
//   },
// ];
