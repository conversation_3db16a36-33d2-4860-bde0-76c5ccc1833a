{"env": {"browser": true, "es2021": true}, "extends": ["next", "next/core-web-vitals", "eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react/jsx-runtime", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "plugins": ["@typescript-eslint", "react", "prettier"], "ignorePatterns": ["tailwind.config.js", "postcss.config.js"], "rules": {"@typescript-eslint/no-unused-expressions": "off", "@typescript-eslint/consistent-type-imports": "error", "object-shorthand": "error", "no-fallthrough": "off", "react/no-unescaped-entities": "off", "react-hooks/exhaustive-deps": "off"}, "settings": {"react": {"version": "detect"}, "import/resolver": {"typescript": {}}}}