'use server';

import { DEFAULT_LOCALE } from '@hi7/configs/dictionary';
import { SUPPORTED_LOCALES } from '@hi7/configs/pathname';
import { headers } from 'next/headers';

export async function getPathname(): Promise<string> {
  return headers().get('x-next-url') ?? '';
}

export async function getBaseurl(): Promise<string> {
  const pathname = await getPathname();
  const [, rawLocale] = pathname.split('/');

  const locale = SUPPORTED_LOCALES.includes(rawLocale)
    ? rawLocale
    : DEFAULT_LOCALE;

  return `/${locale}`;
}
