import ProductBgBottomLeft from '@hi7/assets/background/product-3-bg-bottom-left.svg';
import ProductBgBottomRight from '@hi7/assets/background/product-3-bg-bottom-right.svg';
import ProductBgTopLeft1 from '@hi7/assets/background/product-3-bg-top-left-1.svg';
import ProductBgTopLeft2 from '@hi7/assets/background/product-3-bg-top-left-2.svg';
import ProductBgTopRight from '@hi7/assets/background/product-3-bg-top-right.svg';
import productBgCenter from '@hi7/assets/background/product-3-bg.png';
import Product1 from '@hi7/assets/icon/product-3-1.svg';
import Product2 from '@hi7/assets/icon/product-3-2.svg';

import TriggerAnimation from '@hi7/components/TriggerAnimation';
import type { Dictionary } from '@hi7/interface/dictionary';
import { LocaleProps } from '@hi7/interface/i18n';
import { getDictionary } from '@hi7/lib/i18n';
import Image from 'next/image';
import GetStarted from './GetStarted';

async function page({ params }: LocaleProps) {
  const { locale } = params;
  const dictionary: Dictionary = await getDictionary(locale);
  return (
    <>
      <div className="flex items-center justify-center overflow-hidden bg-linear-[180deg,#172DB1_0%,#90C6FF_50%,#E1EFFF_100%]">
        <div className="w-full">
          <div className="relative lg:min-h-dvh">
            <div className="lg:animate-product-3-bg-right absolute top-[48dvh] right-[-20dvh] h-[45dvh] lg:top-[80px] lg:right-0 lg:h-[100dvh] lg:opacity-0">
              <ProductBgTopRight height="100%" />
            </div>
            <div className="lg:animate-product-3-bg-left-1 absolute top-[-100px] left-[-60px] h-[77dvh] lg:top-[-18%] lg:left-0 lg:z-0 lg:h-[100dvh] lg:translate-x-[-100%]">
              <ProductBgTopLeft1 height="100%" />
            </div>
            <div className="lg:animate-product-3-bg-left-2 absolute top-[-100px] left-[-60px] h-[76dvh] lg:top-[-18%] lg:left-0 lg:z-0 lg:h-[99dvh] lg:translate-x-[-100%] lg:opacity-0">
              <ProductBgTopLeft2 height="100%" />
            </div>

            <div className="relative flex flex-col pt-20 text-white lg:flex-row lg:pt-[20dvh] lg:pl-[120px]">
              <div className="lg:animate-product-3-title flex flex-1 flex-col justify-center self-start px-9 text-center lg:px-0 lg:text-left lg:opacity-0">
                <h1 className="mb-5 text-[40px] leading-[48px] font-bold lg:text-[50px] lg:leading-[55px]">
                  {
                    dictionary.productSolution.customerServiceSaasTool.landing
                      .title
                  }
                </h1>

                <h3 className="text-[20px] leading-7">
                  {
                    dictionary.productSolution.customerServiceSaasTool.landing
                      .desc
                  }
                </h3>
              </div>

              <div className="lg:animate-product-3-picture relative mb-29 h-[50dvh] w-[98%] lg:my-0 lg:mt-[-12dvh] lg:h-[55dvh] lg:w-[20dvw] lg:flex-[1]">
                <Image
                  fill
                  src={productBgCenter}
                  alt={''}
                  className="object-contain"
                />
              </div>
            </div>
          </div>
          <TriggerAnimation>
            <div className="relative flex flex-col items-center px-5 lg:px-[250px] lg:pb-[160px]">
              <div className="lg:animate-product-3-bottom-ring-left absolute top-[-100%] left-[-52%] w-[444px] lg:top-[-10%] lg:left-0 lg:translate-x-[-50%]">
                <ProductBgBottomLeft width="100%" />
              </div>
              <div className="lg:animate-product-3-bottom-ring-right absolute top-[35%] right-[-27%] h-[338px] w-[271px] lg:top-[28%] lg:right-[-5%] lg:translate-x-[100%]">
                <ProductBgBottomRight height="100%" />
              </div>
              <h1 className="lg:animate-product-3-bottom-title mb-16 text-center text-[32px] leading-[38px] font-bold lg:text-[50px] lg:leading-[55px] lg:opacity-0">
                {
                  dictionary.productSolution.customerServiceSaasTool.feature
                    .title
                }
              </h1>
              <div className="lg:animate-product-3-bottom-content relative z-10 grid w-full grid-cols-1 justify-start gap-x-[120px] gap-y-8 pb-10 lg:grid-cols-2 lg:opacity-0">
                <div className="flex flex-col gap-3">
                  <Product1 />
                  <b className="text-[22px] leading-[26px]">
                    {
                      dictionary.productSolution.customerServiceSaasTool.feature
                        .feature1.title
                    }
                  </b>
                  <p className="text-[16px]">
                    {
                      dictionary.productSolution.customerServiceSaasTool.feature
                        .feature1.desc
                    }
                  </p>
                </div>

                <div className="flex flex-col gap-3">
                  <Product2 />
                  <b className="text-[22px] leading-[26px]">
                    {
                      dictionary.productSolution.customerServiceSaasTool.feature
                        .feature2.title
                    }
                  </b>
                  <p className="text-[16px]">
                    {
                      dictionary.productSolution.customerServiceSaasTool.feature
                        .feature2.desc
                    }
                  </p>
                </div>
              </div>
            </div>
          </TriggerAnimation>
        </div>
      </div>
      <GetStarted dictionary={dictionary} locale={locale} />
    </>
  );
}

export default page;
