import type { LinkProps } from '@hi7/components/Link';

type SubItemLink = {
  icon?: React.ReactNode;
  url: string;
  text: string;
};

export type MenuLinkProps = LinkProps & {
  open?: boolean;
  asButton?: boolean;

  icon?: React.ReactNode;
  items?: SubItemLink[];

  children: React.ReactNode | string;
  url?: string;
};

export type MediaProps = {
  url: string;
  icon: React.ReactNode;
  target?: '_blank' | '_parent' | '_self' | '_top';
  children?: React.ReactNode;
};
