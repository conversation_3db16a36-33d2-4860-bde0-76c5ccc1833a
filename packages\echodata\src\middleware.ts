import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import COOKIES from './configs/cookies';
import { DEFAULT_LOCALE } from './configs/dictionary';
import { SUPPORTED_LOCALES } from './configs/pathname';
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './helpers/cookie';

export async function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;
  console.log('Middleware processing path:', pathname);

  const segments = pathname.split('/').filter(Boolean);
  let locale = segments[0];
  const restOfPath = segments.slice(1).join('/');

  let needsRedirect = false;

  // Check and correct locale if necessary
  if (!SUPPORTED_LOCALES.includes(locale)) {
    console.log(
      `Unsupported locale: ${locale}, defaulting to ${DEFAULT_LOCALE}`,
    );
    locale = DEFAULT_LOCALE;
    needsRedirect = true;
  }

  if (needsRedirect) {
    const newPath = `/${locale}${restOfPath ? '/' + restOfPath : ''}`;
    const newUrl = new URL(newPath, request.url);
    newUrl.search = request.nextUrl.search;
    console.log('Redirecting to:', newUrl.toString());

    const response = NextResponse.redirect(newUrl);
    await initVisitorId(request, response);

    return response;
  }

  // Check if the route exists for /product
  return checkRouteExists(request);
}

async function checkRouteExists(request: NextRequest) {
  const response = NextResponse.next();

  await initVisitorId(request, response);

  const pathname = request.nextUrl.pathname;
  response.headers.set('x-next-url', pathname);

  return response;
}

// cannot move to helper, as imported at middleware
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

const initVisitorId = async (request: NextRequest, response: NextResponse) => {
  const cookieHandler = new CookieHandler(request, response);
  const visitorId = cookieHandler.getCookie(COOKIES.visitor)?.value;

  if (visitorId) {
    response.headers.set('x-visitor-id', visitorId);
    return;
  }

  const newVisitorId = generateUUID();
  response.headers.set('x-visitor-id', newVisitorId);
  cookieHandler.setCookie({ name: COOKIES.visitor, value: newVisitorId });
};
export const config = {
  matcher: [
    '/((?!api|_next/static|_next/image|favicon.ico|health-check|feature-flag|videos).*)',
  ],
};
