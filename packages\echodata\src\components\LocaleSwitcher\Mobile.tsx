'use client';

import clsx from 'clsx';
import { usePathname, useRouter } from 'next/navigation';

import Globe from '@hi7/assets/icon/globe.svg';
import { i18n, type Locale } from '@hi7/lib/i18n';
import { useGlobalStore } from '@hi7/provider/ZustandContext';
import { LANGUAGE_NAME } from './config';

export function LocaleSwitcherButton({ onClick }: { onClick: () => void }) {
  return (
    <div
      className={clsx('relative flex cursor-pointer items-center gap-2.5')}
      onClick={onClick}
    >
      <Globe />
    </div>
  );
}

export default function LocaleSwitcher() {
  const pathname = usePathname();
  const router = useRouter();
  const setBaseurl = useGlobalStore((s) => s.setBaseurl);

  const [, currentEndPoint] = pathname.split('/').slice(1) as [Locale, string];

  const handleLocaleChange = (locale: Locale) => {
    setBaseurl(`/${locale}`);
    router.push(`/${locale}${currentEndPoint ? `/${currentEndPoint}` : ''}`);
  };

  return i18n.locales.map((locale) => (
    <button
      key={locale}
      onClick={() => handleLocaleChange(locale)}
      className={clsx('flex cursor-pointer items-center gap-2.5 py-4')}
    >
      {LANGUAGE_NAME[locale] ?? locale}
    </button>
  ));
}
