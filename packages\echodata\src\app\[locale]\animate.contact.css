:root {
  --contact-animation-duration: 1s;
  --contact-animation-half-duration: 0.5s;
  --contact-animation-scale-duration: 5s;
  --contact-animation-scale-half-duration: 2.5s;
}

@theme {
  --animate-contact-top-ring-left: contactTopRingLeft
    var(--contact-animation-duration) ease-in-out forwards;
  @keyframes contactTopRingLeft {
    0% {
      opacity: 0;
      transform: translateX(-235px);
    }

    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }

  --animate-contact-top-ring-right: contactTopRingRight
    var(--contact-animation-duration) ease-in-out forwards;
  @keyframes contactTopRingRight {
    0% {
      opacity: 0;
      transform: translateX(210px);
    }

    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }

  --animate-contact-top-icons-left: contactTopIconsLeft
    var(--contact-animation-duration) ease-in-out forwards;
  @keyframes contactTopIconsLeft {
    0%,
    50% {
      opacity: 0;
      transform: translate(-235px, -235px);
    }

    100% {
      opacity: 1;
      transform: translate(0, 0);
    }
  }

  --animate-contact-top-icons-right: contactTopIconsRight
    var(--contact-animation-duration) ease-in-out forwards;
  @keyframes contactTopIconsRight {
    0%,
    50% {
      opacity: 0;
      transform: translate(210px, -210px);
    }

    100% {
      opacity: 1;
      transform: translate(0, 0);
    }
  }

  --animate-contact-top-icons-scale: contactTopIconsScale
    var(--contact-animation-scale-duration) ease-in-out infinite;
  @keyframes contactTopIconsScale {
    0%,
    100% {
      transform: scale(1.25);
    }

    50% {
      transform: scale(0.8);
    }
  }

  --animate-contact-top-icons-move: contactTopIconsMove
    var(--contact-animation-scale-duration) ease-in-out infinite;
  @keyframes contactTopIconsMove {
    0%,
    100% {
      transform: translateY(-15px);
    }

    50% {
      transform: translateY(0);
    }
  }

  --animate-contact-top-icons-set-1: var(--animate-contact-top-icons-left),
    var(--animate-contact-top-icons-move);

  --animate-contact-top-icons-set-2: var(--animate-contact-top-icons-left),
    var(--animate-contact-top-icons-scale);

  --animate-contact-top-icons-set-3: var(--animate-contact-top-icons-right),
    var(--animate-contact-top-icons-move);

  --animate-contact-top-icons-set-4: var(--animate-contact-top-icons-right),
    var(--animate-contact-top-icons-scale);

  --animate-contact-top-title: contactTopTitle var(--contact-animation-duration)
    ease-in-out forwards;
  @keyframes contactTopTitle {
    0%,
    50% {
      opacity: 0;
      transform: translateY(20px);
    }

    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  --animate-contact-top-content: contactTopContent
    var(--contact-animation-duration) ease-in-out forwards;
  @keyframes contactTopContent {
    0%,
    50% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }

  --animate-contact-bottom-title: contactBottomTitle
    var(--contact-animation-duration) ease-in-out forwards;
  @keyframes contactBottomTitle {
    0% {
      opacity: 0;
      transform: translateY(-200%);
    }

    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  --animate-contact-bottom-0: contactBottom0 var(--contact-animation-duration)
    ease-in-out forwards;
  @keyframes contactBottom0 {
    0% {
      opacity: 0;
      transform: translateX(-100%);
    }

    100% {
      opacity: 1;
      transform: translateX(100%);
    }
  }

  --animate-contact-bottom-1: contactBottom1 var(--contact-animation-duration)
    ease-in-out forwards;
  @keyframes contactBottom1 {
    0% {
      opacity: 0;
      transform: translateY(100%);
    }

    100% {
      opacity: 1;
      transform: translateY(-100%);
    }
  }

  --animate-contact-bottom-2: contactBottom2 var(--contact-animation-duration)
    ease-in-out forwards;
  @keyframes contactBottom2 {
    0% {
      opacity: 0;
      transform: translateX(100%);
    }

    100% {
      opacity: 1;
      transform: translateX(-100%);
    }
  }
}
